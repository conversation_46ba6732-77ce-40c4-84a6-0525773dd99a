"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeAttachments, setActiveAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado\n    const saveLastUsedModel = async (modelId)=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            // Verificar se o documento existe\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                // Atualizar documento existente\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                });\n            } else {\n                // Criar novo documento\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                }, {\n                    merge: true\n                });\n            }\n            console.log(\"Last used model saved:\", modelId);\n        } catch (error) {\n            console.error(\"Error saving last used model:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado\n    const loadLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        saveLastUsedModel(modelId);\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments)=>{\n        console.log(\"=== DEBUG: CHATAREA HANDLE SEND MESSAGE ===\");\n        console.log(\"Mensagem:\", message);\n        console.log(\"Anexos recebidos:\", (attachments === null || attachments === void 0 ? void 0 : attachments.length) || 0);\n        console.log(\"Anexos detalhes:\", JSON.stringify(attachments, null, 2));\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            var _msg_attachments;\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // Carregar o nome do chat recém-criado com um pequeno delay\n                setTimeout(()=>{\n                    if (chatIdToUse) {\n                        loadChatName(chatIdToUse);\n                    }\n                }, 100);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: attachments || []\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString()\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                setChatName(chatData.name || \"Conversa sem nome\");\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== DEBUG: MENSAGENS CONVERTIDAS ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado quando o componente montar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadLastUsedModel();\n        }\n    }, [\n        user\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n        } else if (!currentChat) {\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        const messageIndex = messages.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        const messageToRegenerate = messages[messageIndex];\n        // Remover todas as mensagens a partir da mensagem selecionada (inclusive)\n        const messagesBeforeRegeneration = messages.slice(0, messageIndex);\n        setMessages(messagesBeforeRegeneration);\n        // Preparar o conteúdo da mensagem para regenerar\n        setMessage(messageToRegenerate.content);\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        try {\n            // Deletar mensagens posteriores do Firebase Storage\n            for(let i = messageIndex; i < messages.length; i++){\n                const msgToDelete = messages[i];\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString()\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao atualizar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao atualizar mensagem:\", error);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: chatInterfaceRef.current.scrollHeight,\n            behavior: \"smooth\"\n        });\n    };\n    const handleFullscreenToggle = ()=>{\n        setIsFullscreen(!isFullscreen);\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Sincronizar estado quando currentChat mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Limpar mensagens quando mudar para um chat diferente ou para área inicial\n        if (currentChat !== actualChatId) {\n            setMessages([]);\n        }\n        setActualChatId(currentChat);\n    }, [\n        currentChat\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen \".concat(isFullscreen ? \"fixed inset-0 z-50 bg-gradient-rafthor\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                isFullscreen: isFullscreen,\n                onFullscreenToggle: handleFullscreenToggle,\n                onDownload: handleDownloadModal,\n                isLoading: isLoading,\n                attachmentsCount: 0,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 668,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0 overflow-hidden\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 683,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 696,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 721,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 666,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"749WcwwgOpJgoAig8fKGk7uj6is=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});