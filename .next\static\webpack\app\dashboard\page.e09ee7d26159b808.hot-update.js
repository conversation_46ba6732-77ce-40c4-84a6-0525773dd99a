"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/AttachmentsModal.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AttachmentsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AttachmentsModal(param) {\n    let { isOpen, onClose, messages, onAttachmentsChange, enabledAttachments = [] } = param;\n    _s();\n    const [attachmentsWithState, setAttachmentsWithState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [expandedImage, setExpandedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Coletar todos os anexos das mensagens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) return;\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                message.attachments.forEach((attachment)=>{\n                    // Verificar se este anexo já está na lista de habilitados\n                    const isCurrentlyEnabled = enabledAttachments.some((enabled)=>enabled.id === attachment.id);\n                    allAttachments.push({\n                        ...attachment,\n                        isEnabled: isCurrentlyEnabled,\n                        messageId: message.id,\n                        messageTimestamp: message.timestamp\n                    });\n                });\n            }\n        });\n        // Ordenar por timestamp (mais recentes primeiro)\n        allAttachments.sort((a, b)=>b.messageTimestamp - a.messageTimestamp);\n        setAttachmentsWithState(allAttachments);\n    }, [\n        isOpen,\n        messages,\n        enabledAttachments\n    ]);\n    // Função para notificar mudanças nos anexos habilitados\n    const notifyAttachmentsChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const enabledAttachments = attachmentsWithState.filter((att)=>att.isEnabled).map((att)=>({\n                id: att.id,\n                type: att.type,\n                filename: att.filename,\n                url: att.url,\n                size: att.size,\n                uploadedAt: att.uploadedAt,\n                base64Data: att.base64Data,\n                chatName: att.chatName,\n                storagePath: att.storagePath\n            }));\n        onAttachmentsChange(enabledAttachments);\n    }, [\n        attachmentsWithState,\n        onAttachmentsChange\n    ]);\n    // Função para fechar o modal e notificar mudanças\n    const handleClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        notifyAttachmentsChange();\n        onClose();\n    }, [\n        notifyAttachmentsChange,\n        onClose\n    ]);\n    const toggleAttachment = (attachmentId)=>{\n        setAttachmentsWithState((prev)=>prev.map((att)=>att.id === attachmentId ? {\n                    ...att,\n                    isEnabled: !att.isEnabled\n                } : att));\n    };\n    const toggleAll = (enable)=>{\n        setAttachmentsWithState((prev)=>prev.map((att)=>({\n                    ...att,\n                    isEnabled: enable\n                })));\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const formatDate = (timestamp)=>{\n        return new Date(timestamp).toLocaleString(\"pt-BR\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const handleImageClick = (url)=>{\n        setExpandedImage(url);\n    };\n    const handleDownload = async (attachment)=>{\n        try {\n            // Usar nossa API route para contornar problemas de CORS\n            const downloadUrl = \"/api/download/attachment?url=\".concat(encodeURIComponent(attachment.url), \"&filename=\").concat(encodeURIComponent(attachment.filename));\n            // Criar link para download\n            const link = document.createElement(\"a\");\n            link.href = downloadUrl;\n            link.download = attachment.filename;\n            link.target = \"_blank\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        } catch (error) {\n            console.error(\"Erro ao baixar arquivo:\", error);\n        }\n    };\n    const enabledCount = attachmentsWithState.filter((att)=>att.isEnabled).length;\n    const totalCount = attachmentsWithState.length;\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n            onClick: (e)=>e.target === e.currentTarget && handleClose(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        scale: 0.95,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    exit: {\n                        scale: 0.95,\n                        opacity: 0\n                    },\n                    className: \"bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm border border-white/20 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden mx-4 lg:mx-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: \"Gerenciar Anexos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm mt-1\",\n                                            children: \"Selecione quais anexos incluir no pr\\xf3ximo prompt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClose,\n                                    className: \"text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-cyan-400\",\n                                                        children: enabledCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/60\",\n                                                        children: [\n                                                            \"/\",\n                                                            totalCount,\n                                                            \" anexos selecionados\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            totalCount === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-amber-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Nenhum anexo encontrado neste chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleAll(true),\n                                                className: \"px-4 py-2 bg-green-600/20 hover:bg-green-600/30 text-green-400  border border-green-500/30 rounded-lg transition-colors text-sm\",\n                                                children: \"Selecionar Todos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleAll(false),\n                                                className: \"px-4 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-400  border border-red-500/30 rounded-lg transition-colors text-sm\",\n                                                children: \"Desmarcar Todos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-6\",\n                            children: totalCount === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-8 h-8 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white mb-2\",\n                                        children: \"Nenhum anexo encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60\",\n                                        children: \"Este chat ainda n\\xe3o possui anexos. Envie imagens ou PDFs para come\\xe7ar.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: attachmentsWithState.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\\n                      border rounded-xl p-4 transition-all duration-200\\n                      \".concat(attachment.isEnabled ? \"bg-blue-600/10 border-blue-500/30 shadow-lg shadow-blue-500/10\" : \"bg-gray-600/10 border-gray-500/20\", \"\\n                    \"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>toggleAttachment(attachment.id),\n                                                    className: \"\\n                          w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all\\n                          \".concat(attachment.isEnabled ? \"bg-blue-600 border-blue-500 text-white\" : \"border-gray-400 hover:border-blue-400\", \"\\n                        \"),\n                                                    children: attachment.isEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 50\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 mb-2\",\n                                                            children: [\n                                                                attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-400 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-red-400 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-white truncate\",\n                                                                    children: attachment.filename\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/60 flex-shrink-0\",\n                                                                    children: formatFileSize(attachment.size)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-white/60 mb-3\",\n                                                            children: [\n                                                                \"Enviado em \",\n                                                                formatDate(attachment.messageTimestamp)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        attachment.type === \"image\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: attachment.url,\n                                                                    alt: attachment.filename,\n                                                                    className: \"max-w-full h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                                    style: {\n                                                                        maxHeight: \"150px\"\n                                                                    },\n                                                                    onClick: ()=>handleImageClick(attachment.url)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleImageClick(attachment.url),\n                                                                    className: \"absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70 transition-all\",\n                                                                    title: \"Expandir imagem\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDownload(attachment),\n                                                                className: \"flex items-center gap-1 text-xs text-blue-400 hover:text-blue-300 transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Baixar\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, attachment.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-t border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: enabledCount > 0 ? \"\".concat(enabledCount, \" anexo\").concat(enabledCount !== 1 ? \"s\" : \"\", \" ser\\xe1\").concat(enabledCount !== 1 ? \"\\xe3o\" : \"\", \" inclu\\xeddo\").concat(enabledCount !== 1 ? \"s\" : \"\", \" no pr\\xf3ximo prompt\") : \"Nenhum anexo ser\\xe1 inclu\\xeddo no pr\\xf3ximo prompt\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleClose,\n                                        className: \"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                                        children: \"Conclu\\xeddo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                expandedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/80 backdrop-blur-sm z-60 flex items-center justify-center p-4\",\n                    onClick: ()=>setExpandedImage(null),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.img, {\n                            initial: {\n                                scale: 0.8,\n                                opacity: 0\n                            },\n                            animate: {\n                                scale: 1,\n                                opacity: 1\n                            },\n                            exit: {\n                                scale: 0.8,\n                                opacity: 0\n                            },\n                            src: expandedImage,\n                            alt: \"Imagem expandida\",\n                            className: \"max-w-full max-h-full object-contain rounded-lg\",\n                            onClick: (e)=>e.stopPropagation()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setExpandedImage(null),\n                            className: \"absolute top-4 right-4 text-white/80 hover:text-white p-2 hover:bg-white/10 rounded-lg transition-all\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_s(AttachmentsModal, \"gmAii2nPfylSsq2yVYcttqJGSXU=\");\n_c = AttachmentsModal;\nvar _c;\n$RefreshReg$(_c, \"AttachmentsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\n"));

/***/ })

});