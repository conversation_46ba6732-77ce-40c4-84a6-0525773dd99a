"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/attachmentService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/attachmentService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachmentService: function() { return /* binding */ attachmentService; }\n/* harmony export */ });\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass AttachmentService {\n    /**\n   * Valida se o arquivo é suportado\n   */ validateFile(file) {\n        // Verificar tamanho\n        if (file.size > this.MAX_FILE_SIZE) {\n            return {\n                isValid: false,\n                error: \"Arquivo muito grande. M\\xe1ximo permitido: 10MB\"\n            };\n        }\n        // Verificar tipo\n        const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n        const isPdf = file.type === this.SUPPORTED_PDF_TYPE;\n        if (!isImage && !isPdf) {\n            return {\n                isValid: false,\n                error: \"Tipo de arquivo n\\xe3o suportado. Use PNG, JPEG, WebP ou PDF\"\n            };\n        }\n        return {\n            isValid: true\n        };\n    }\n    /**\n   * Converte arquivo para base64\n   */ async fileToBase64(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                // Remover o prefixo data:type;base64,\n                const base64 = result.split(\",\")[1];\n                resolve(base64);\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(file);\n        });\n    }\n    /**\n   * Gera ID único para anexo\n   */ generateAttachmentId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Faz upload do arquivo para Firebase Storage\n   */ async uploadAttachment(params) {\n        const { file, username, chatId } = params;\n        // Validar arquivo\n        const validation = this.validateFile(file);\n        if (!validation.isValid) {\n            throw new Error(validation.error);\n        }\n        try {\n            // Gerar ID único para o anexo\n            const attachmentId = this.generateAttachmentId();\n            // Determinar tipo\n            const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n            const type = isImage ? \"image\" : \"pdf\";\n            // Criar caminho no Storage\n            const storagePath = \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/anexos/\").concat(attachmentId, \"_\").concat(file.name);\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, storagePath);\n            // Upload do arquivo\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.uploadBytes)(storageRef, file);\n            // Obter URL de download\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(storageRef);\n            // Preparar metadados\n            const metadata = {\n                id: attachmentId,\n                type,\n                filename: file.name,\n                url: downloadURL,\n                size: file.size,\n                uploadedAt: Date.now(),\n                storagePath,\n                isActive: true // Por padrão, anexos são ativos\n            };\n            // Para PDFs, também converter para base64 para envio ao OpenRouter\n            let base64Data;\n            if (type === \"pdf\") {\n                base64Data = await this.fileToBase64(file);\n                metadata.base64Data = base64Data;\n            }\n            return {\n                metadata,\n                base64Data\n            };\n        } catch (error) {\n            console.error(\"Erro ao fazer upload do anexo:\", error);\n            throw new Error(\"Falha no upload do arquivo. Tente novamente.\");\n        }\n    }\n    /**\n   * Processa múltiplos arquivos\n   */ async uploadMultipleAttachments(files, username, chatId) {\n        const results = [];\n        for (const file of files){\n            try {\n                const result = await this.uploadAttachment({\n                    file,\n                    username,\n                    chatId\n                });\n                results.push(result);\n            } catch (error) {\n                console.error(\"Erro ao processar arquivo \".concat(file.name, \":\"), error);\n            // Continuar com os outros arquivos\n            }\n        }\n        return results;\n    }\n    /**\n   * Prepara anexos para envio ao OpenRouter\n   */ prepareAttachmentsForOpenRouter(attachments) {\n        const openRouterContent = [];\n        // Filtrar apenas anexos ativos\n        const activeAttachments = attachments.filter((att)=>att.isActive !== false);\n        for (const attachment of activeAttachments){\n            if (attachment.type === \"image\") {\n                // Para imagens, usar URL\n                openRouterContent.push({\n                    type: \"image_url\",\n                    image_url: {\n                        url: attachment.url\n                    }\n                });\n            } else if (attachment.type === \"pdf\" && attachment.base64Data) {\n                // Para PDFs, usar base64 com formato file\n                const dataUrl = \"data:application/pdf;base64,\".concat(attachment.base64Data);\n                openRouterContent.push({\n                    type: \"file\",\n                    file: {\n                        filename: attachment.filename,\n                        file_data: dataUrl\n                    }\n                });\n            }\n        }\n        return openRouterContent;\n    }\n    /**\n   * Prepara plugins para PDFs (engine mistral-ocr)\n   */ preparePDFPlugins(attachments) {\n        const hasPDF = attachments.some((att)=>att.type === \"pdf\");\n        if (!hasPDF) {\n            return [];\n        }\n        return [\n            {\n                id: \"file-parser\",\n                pdf: {\n                    engine: \"mistral-ocr\"\n                }\n            }\n        ];\n    }\n    /**\n   * Limpa anexos temporários (se necessário)\n   */ async cleanupTemporaryAttachments(attachments) {\n        // Implementar limpeza se necessário\n        // Por enquanto, mantemos os arquivos no Storage\n        console.log(\"Cleanup de anexos tempor\\xe1rios:\", attachments.length);\n    }\n    constructor(){\n        this.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n        ;\n        this.SUPPORTED_IMAGE_TYPES = [\n            \"image/png\",\n            \"image/jpeg\",\n            \"image/webp\"\n        ];\n        this.SUPPORTED_PDF_TYPE = \"application/pdf\";\n    }\n}\n// Exportar instância singleton\nconst attachmentService = new AttachmentService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (attachmentService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/attachmentService.ts\n"));

/***/ })

});