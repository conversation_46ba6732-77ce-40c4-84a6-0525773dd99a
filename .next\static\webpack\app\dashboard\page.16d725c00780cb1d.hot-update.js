"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado\n    const saveLastUsedModel = async (modelId)=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            // Verificar se o documento existe\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                // Atualizar documento existente\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                });\n            } else {\n                // Criar novo documento\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                }, {\n                    merge: true\n                });\n            }\n            console.log(\"Last used model saved:\", modelId);\n        } catch (error) {\n            console.error(\"Error saving last used model:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado\n    const loadLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        saveLastUsedModel(modelId);\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments)=>{\n        console.log(\"=== DEBUG: CHATAREA HANDLE SEND MESSAGE ===\");\n        console.log(\"Mensagem:\", message);\n        console.log(\"Anexos recebidos:\", (attachments === null || attachments === void 0 ? void 0 : attachments.length) || 0);\n        console.log(\"Anexos detalhes:\", JSON.stringify(attachments, null, 2));\n        // Filtrar apenas anexos ativos\n        const activeAttachmentsToSend = getActiveAttachments(attachments);\n        console.log(\"Anexos ativos para envio:\", activeAttachmentsToSend.length);\n        console.log(\"Anexos ativos detalhes:\", JSON.stringify(activeAttachmentsToSend, null, 2));\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            var _msg_attachments;\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // Carregar o nome do chat recém-criado com um pequeno delay\n                setTimeout(()=>{\n                    if (chatIdToUse) {\n                        loadChatName(chatIdToUse);\n                    }\n                }, 100);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: activeAttachmentsToSend\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString()\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                setChatName(chatData.name || \"Conversa sem nome\");\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== DEBUG: MENSAGENS CONVERTIDAS ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado quando o componente montar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadLastUsedModel();\n        }\n    }, [\n        user\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n        } else if (!currentChat) {\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        const messageIndex = messages.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        const messageToRegenerate = messages[messageIndex];\n        // Remover todas as mensagens a partir da mensagem selecionada (inclusive)\n        const messagesBeforeRegeneration = messages.slice(0, messageIndex);\n        setMessages(messagesBeforeRegeneration);\n        // Preparar o conteúdo da mensagem para regenerar\n        setMessage(messageToRegenerate.content);\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        try {\n            // Deletar mensagens posteriores do Firebase Storage\n            for(let i = messageIndex; i < messages.length; i++){\n                const msgToDelete = messages[i];\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString()\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao atualizar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao atualizar mensagem:\", error);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: chatInterfaceRef.current.scrollHeight,\n            behavior: \"smooth\"\n        });\n    };\n    const handleFullscreenToggle = ()=>{\n        setIsFullscreen(!isFullscreen);\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            return prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            return {\n                                ...attachment,\n                                isActive: !attachment.isActive\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        return attachments.filter((attachment)=>attachment.isActive !== false);\n    };\n    // Sincronizar estado quando currentChat mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Limpar mensagens quando mudar para um chat diferente ou para área inicial\n        if (currentChat !== actualChatId) {\n            setMessages([]);\n        }\n        setActualChatId(currentChat);\n    }, [\n        currentChat\n    ]);\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen \".concat(isFullscreen ? \"fixed inset-0 z-50 bg-gradient-rafthor\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                isFullscreen: isFullscreen,\n                onFullscreenToggle: handleFullscreenToggle,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 726,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0 overflow-hidden\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 743,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 742,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 755,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 772,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 780,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 788,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 724,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"bRRkI0MWfsaVKuRTeJ3aRUAJvcg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});