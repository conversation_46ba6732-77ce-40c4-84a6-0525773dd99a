"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado\n    const saveLastUsedModel = async (modelId)=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            // Verificar se o documento existe\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                // Atualizar documento existente\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                });\n            } else {\n                // Criar novo documento\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                }, {\n                    merge: true\n                });\n            }\n            console.log(\"Last used model saved:\", modelId);\n        } catch (error) {\n            console.error(\"Error saving last used model:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado\n    const loadLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        saveLastUsedModel(modelId);\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments)=>{\n        console.log(\"=== DEBUG: CHATAREA HANDLE SEND MESSAGE ===\");\n        console.log(\"Mensagem:\", message);\n        console.log(\"Anexos recebidos:\", (attachments === null || attachments === void 0 ? void 0 : attachments.length) || 0);\n        console.log(\"Anexos detalhes:\", JSON.stringify(attachments, null, 2));\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            var _msg_attachments;\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // Carregar o nome do chat recém-criado com um pequeno delay\n                setTimeout(()=>{\n                    if (chatIdToUse) {\n                        loadChatName(chatIdToUse);\n                    }\n                }, 100);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: attachments || []\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString()\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                setChatName(chatData.name || \"Conversa sem nome\");\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== DEBUG: MENSAGENS CONVERTIDAS ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado quando o componente montar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadLastUsedModel();\n        }\n    }, [\n        user\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n        } else if (!currentChat) {\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        const messageIndex = messages.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        const messageToRegenerate = messages[messageIndex];\n        // Remover todas as mensagens a partir da mensagem selecionada (inclusive)\n        const messagesBeforeRegeneration = messages.slice(0, messageIndex);\n        setMessages(messagesBeforeRegeneration);\n        // Preparar o conteúdo da mensagem para regenerar\n        setMessage(messageToRegenerate.content);\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        try {\n            // Deletar mensagens posteriores do Firebase Storage\n            for(let i = messageIndex; i < messages.length; i++){\n                const msgToDelete = messages[i];\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString()\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao atualizar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao atualizar mensagem:\", error);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: chatInterfaceRef.current.scrollHeight,\n            behavior: \"smooth\"\n        });\n    };\n    const handleFullscreenToggle = ()=>{\n        setIsFullscreen(!isFullscreen);\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Sincronizar estado quando currentChat mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Limpar mensagens quando mudar para um chat diferente ou para área inicial\n        if (currentChat !== actualChatId) {\n            setMessages([]);\n        }\n        setActualChatId(currentChat);\n    }, [\n        currentChat\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen \".concat(isFullscreen ? \"fixed inset-0 z-50 bg-gradient-rafthor\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                isFullscreen: isFullscreen,\n                onFullscreenToggle: handleFullscreenToggle,\n                onDownload: handleDownloadModal,\n                isLoading: isLoading,\n                attachmentsCount: 0,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 665,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0 overflow-hidden\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 681,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 680,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 693,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 710,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 663,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"em3pPb9uTKX0kA37qkrFLpj18+s=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/InputBar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/InputBar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InputBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AI_MODELS = [\n    {\n        id: \"gpt-4.1-nano\",\n        name: \"GPT-4.1 Nano\",\n        description: \"R\\xe1pido e eficiente\"\n    },\n    {\n        id: \"gpt-4-turbo\",\n        name: \"GPT-4 Turbo\",\n        description: \"Mais poderoso\"\n    },\n    {\n        id: \"claude-3-sonnet\",\n        name: \"Claude 3 Sonnet\",\n        description: \"Criativo e preciso\"\n    },\n    {\n        id: \"gemini-pro\",\n        name: \"Gemini Pro\",\n        description: \"Multimodal\"\n    }\n];\nfunction InputBar(param) {\n    let { message, setMessage, onSendMessage, isLoading, selectedModel, onModelChange, onScrollToTop, onScrollToBottom, isStreaming = false, onCancelStreaming, onOpenModelModal, username, chatId } = param;\n    _s();\n    const [webSearchEnabled, setWebSearchEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedAttachments, setProcessedAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const adjustHeightTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (adjustHeightTimeoutRef.current) {\n                clearTimeout(adjustHeightTimeoutRef.current);\n            }\n        };\n    }, []);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        setMessage(e.target.value);\n        // Debounce o ajuste de altura para melhor performance\n        if (adjustHeightTimeoutRef.current) {\n            clearTimeout(adjustHeightTimeoutRef.current);\n        }\n        adjustHeightTimeoutRef.current = setTimeout(()=>{\n            adjustTextareaHeight();\n        }, 16); // ~60fps\n    };\n    const handleSend = ()=>{\n        if (((message === null || message === void 0 ? void 0 : message.trim()) || attachments.length > 0) && !isLoading && !isUploading) {\n            console.log(\"=== DEBUG: ENVIANDO MENSAGEM ===\");\n            console.log(\"Mensagem:\", message);\n            console.log(\"Anexos locais:\", attachments.length);\n            console.log(\"Anexos processados:\", processedAttachments.length);\n            console.log(\"Anexos processados detalhes:\", processedAttachments);\n            onSendMessage(processedAttachments);\n            // Limpar anexos após envio\n            setAttachments([]);\n            setProcessedAttachments([]);\n        }\n    };\n    const handleAttachment = ()=>{\n        if (fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || !username || !chatId) return;\n        setIsUploading(true);\n        try {\n            // Primeiro, adicionar arquivos localmente para preview\n            const localAttachments = [];\n            for (const file of Array.from(files)){\n                const attachment = {\n                    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),\n                    filename: file.name,\n                    type: file.type.startsWith(\"image/\") ? \"image\" : \"document\",\n                    file\n                };\n                localAttachments.push(attachment);\n            }\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...localAttachments\n                ]);\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadMultipleAttachments(Array.from(files), username, chatId);\n            // Atualizar com metadados dos arquivos processados\n            setProcessedAttachments((prev)=>[\n                    ...prev,\n                    ...uploadedAttachments.map((att)=>att.metadata)\n                ]);\n        } catch (error) {\n            console.error(\"Erro ao processar arquivos:\", error);\n            // Remover anexos que falharam\n            setAttachments((prev)=>prev.filter((att)=>!Array.from(files).some((file)=>file.name === att.filename)));\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const removeAttachment = (id)=>{\n        // Encontrar o anexo para obter o filename\n        const attachment = attachments.find((att)=>att.id === id);\n        if (attachment) {\n            // Remover do estado local\n            setAttachments((prev)=>prev.filter((att)=>att.id !== id));\n            // Remover dos anexos processados também\n            setProcessedAttachments((prev)=>prev.filter((att)=>att.filename !== attachment.filename));\n        }\n    };\n    const handleWebSearch = ()=>{\n        setWebSearchEnabled(!webSearchEnabled);\n    };\n    const adjustTextareaHeight = ()=>{\n        const textarea = textareaRef.current;\n        if (textarea) {\n            // Otimizar para evitar múltiplos reflows\n            const currentHeight = textarea.style.height;\n            textarea.style.height = \"auto\";\n            const newHeight = Math.min(textarea.scrollHeight, 120) + \"px\";\n            // Só atualizar se a altura realmente mudou\n            if (currentHeight !== newHeight) {\n                textarea.style.height = newHeight;\n            }\n        }\n    };\n    const isWebSearchEnabled = ()=>{\n        return selectedModel.includes(\"openrouter\") || selectedModel.includes(\"gpt-4\");\n    };\n    const currentModel = AI_MODELS.find((model)=>model.id === selectedModel);\n    const modelName = currentModel ? currentModel.name : selectedModel;\n    const selectedAttachmentsCount = attachments.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-3 sm:p-4 lg:p-6 border-t border-blue-700/30 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto relative z-10\",\n                children: [\n                    attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 sm:gap-3\",\n                            children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20\",\n                                    children: [\n                                        attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium\",\n                                            children: attachment.filename\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>removeAttachment(attachment.id),\n                                            className: \"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3.5 h-3.5 sm:w-4 sm:h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, attachment.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-2 sm:space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 sm:space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onOpenModelModal,\n                                                        className: \"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105\",\n                                                        title: \"Selecionar modelo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAttachment,\n                                                        disabled: isUploading,\n                                                        className: \"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105\",\n                                                        title: \"Anexar arquivo\",\n                                                        children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isWebSearchEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleWebSearch,\n                                                        className: \"p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm \".concat(webSearchEnabled ? \"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20\" : \"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20\"),\n                                                        title: \"Busca na web (OpenRouter) - \".concat(webSearchEnabled ? \"Ativada\" : \"Desativada\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    ref: textareaRef,\n                                                    value: message,\n                                                    onChange: handleInputChange,\n                                                    onKeyDown: handleKeyPress,\n                                                    className: \"w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm leading-relaxed min-h-[44px] max-h-[120px] selection:bg-blue-500/30\",\n                                                    rows: 1,\n                                                    placeholder: \"Digite sua mensagem aqui... ✨\",\n                                                    disabled: isLoading || isStreaming\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 15\n                                            }, this),\n                                            isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onCancelStreaming,\n                                                className: \"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30\",\n                                                title: \"Parar gera\\xe7\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSend,\n                                                disabled: !(message === null || message === void 0 ? void 0 : message.trim()) && attachments.length === 0 || isLoading || isUploading || isStreaming,\n                                                className: \"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30\",\n                                                title: \"Enviar mensagem\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-8 left-4 flex items-center space-x-2\",\n                                        children: [\n                                            selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-300\",\n                                                        children: \"Modelo: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-cyan-300 font-medium\",\n                                                        children: modelName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedAttachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-300\",\n                                                        children: \"Anexos: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-200 font-medium\",\n                                                        children: selectedAttachmentsCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-2\",\n                                children: [\n                                    onScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onScrollToTop,\n                                        className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                        title: \"Ir para o topo\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2.5,\n                                                    d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this),\n                                    onScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onScrollToBottom,\n                                        className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                        title: \"Ir para o final\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2.5,\n                                                    d: \"M19 14l-7 7m0 0l-7-7m7 7V4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        accept: \"image/png,image/jpeg,image/webp,application/pdf\",\n                        onChange: handleFileSelect,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_s(InputBar, \"NATbgEVliPa57PsWdc1QpV4trNc=\");\n_c = InputBar;\nvar _c;\n$RefreshReg$(_c, \"InputBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/InputBar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/Upperbar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/Upperbar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Upperbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Hook para tempo de sessão\nconst useSessionTime = (chatId)=>{\n    _s();\n    const [sessionTime, setSessionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0s\");\n    const [startTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Função para formatar tempo de forma inteligente\n    const formatTime = (timeInMs)=>{\n        const totalSeconds = Math.floor(timeInMs / 1000);\n        const totalMinutes = Math.floor(totalSeconds / 60);\n        const totalHours = Math.floor(totalMinutes / 60);\n        const totalDays = Math.floor(totalHours / 24);\n        const totalMonths = Math.floor(totalDays / 30);\n        const seconds = totalSeconds % 60;\n        const minutes = totalMinutes % 60;\n        const hours = totalHours % 24;\n        const days = totalDays % 30;\n        // Menos de 1 minuto: apenas segundos\n        if (totalMinutes === 0) {\n            return \"\".concat(totalSeconds, \"s\");\n        }\n        // Menos de 1 hora: MM:SS\n        if (totalHours === 0) {\n            return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        // Menos de 1 dia: HH:MM:SS\n        if (totalDays === 0) {\n            return \"\".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        // Menos de 1 mês: Xd HH:MM\n        if (totalMonths === 0) {\n            return \"\".concat(days, \"d \").concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n        }\n        // 1 mês ou mais: Xm Xd HH:MM\n        return \"\".concat(totalMonths, \"m \").concat(days, \"d \").concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatId) {\n            intervalRef.current = setInterval(()=>{\n                const elapsed = Date.now() - startTime;\n                const formattedTime = formatTime(elapsed);\n                setSessionTime(formattedTime);\n            }, 1000);\n        }\n        return ()=>{\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n                intervalRef.current = null;\n            }\n        };\n    }, [\n        chatId,\n        startTime\n    ]);\n    return sessionTime;\n};\n_s(useSessionTime, \"D9h1eTTjB/UJUr/8kCtwXx6uawQ=\");\nfunction Upperbar(param) {\n    let { currentChat, chatName, aiModel, isFullscreen, onFullscreenToggle, onDownload, isLoading = false, attachmentsCount = 0, aiMetadata } = param;\n    _s1();\n    const sessionTime = useSessionTime(currentChat);\n    const handleAttachments = ()=>{\n        console.log(\"Anexos clicado\");\n    };\n    const handleStats = ()=>{\n        console.log(\"Estat\\xedsticas clicado\");\n    };\n    const handleDownload = ()=>{\n        if (onDownload) {\n            onDownload();\n        } else {\n            console.log(\"Download clicado\");\n        }\n    };\n    const toggleFullscreen = ()=>{\n        onFullscreenToggle();\n    };\n    const currentModel = aiModel || \"GPT-4.1 Nano\";\n    const displayChatName = chatName || (currentChat ? \"Chat \".concat(currentChat) : \"Nova Conversa\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-14 sm:h-16 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border-b border-blue-700/30 shadow-xl relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-between px-3 sm:px-4 lg:px-6 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-900/30 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm font-medium text-blue-100 truncate\",\n                                        children: currentModel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    (aiMetadata === null || aiMetadata === void 0 ? void 0 : aiMetadata.usedCoT) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-1.5 sm:px-2 py-0.5 sm:py-1 bg-cyan-600/20 text-cyan-300 text-xs rounded-full border border-cyan-500/30 hidden sm:block\",\n                                        children: \"CoT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-4 sm:h-6 bg-blue-600/30 hidden sm:block\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1.5 sm:space-x-2 bg-blue-900/20 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 hidden sm:flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 text-blue-300\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm text-blue-200 font-mono\",\n                                        children: sessionTime\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-1/2 transform -translate-x-1/2 hidden sm:block\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 sm:h-8 bg-blue-700/30 rounded-lg sm:rounded-xl w-32 sm:w-48 backdrop-blur-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 shadow-lg max-w-xs lg:max-w-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-cyan-400 rounded-full shadow-lg shadow-cyan-400/50 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-sm sm:text-lg font-semibold text-white truncate\",\n                                    children: displayChatName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleFullscreen,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 hidden sm:block\",\n                                title: \"Tela cheia\",\n                                children: isFullscreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAttachments,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 relative\",\n                                title: \"Anexos\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    attachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 bg-cyan-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium shadow-lg\",\n                                        children: attachmentsCount > 99 ? \"99+\" : attachmentsCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStats,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105\",\n                                title: \"Estat\\xedsticas\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownload,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/30 hover:scale-105 border border-blue-500/30\",\n                                title: \"Download\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s1(Upperbar, \"QJdsJtw+rWTUW8x2bLmlvIfMLsk=\", false, function() {\n    return [\n        useSessionTime\n    ];\n});\n_c = Upperbar;\nvar _c;\n$RefreshReg$(_c, \"Upperbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\n"));

/***/ })

});