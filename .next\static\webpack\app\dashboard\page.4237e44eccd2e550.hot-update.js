"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/AttachmentsModal.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AttachmentsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Download,Eye,FileText,Image,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AttachmentsModal(param) {\n    let { isOpen, onClose, messages, onAttachmentsChange, enabledAttachments = [] } = param;\n    _s();\n    const [attachmentsWithState, setAttachmentsWithState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [expandedImage, setExpandedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Coletar todos os anexos das mensagens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) return;\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                message.attachments.forEach((attachment)=>{\n                    allAttachments.push({\n                        ...attachment,\n                        isEnabled: true,\n                        messageId: message.id,\n                        messageTimestamp: message.timestamp\n                    });\n                });\n            }\n        });\n        // Ordenar por timestamp (mais recentes primeiro)\n        allAttachments.sort((a, b)=>b.messageTimestamp - a.messageTimestamp);\n        setAttachmentsWithState(allAttachments);\n    }, [\n        isOpen,\n        messages\n    ]);\n    // Função para notificar mudanças nos anexos habilitados\n    const notifyAttachmentsChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const enabledAttachments = attachmentsWithState.filter((att)=>att.isEnabled).map((att)=>({\n                id: att.id,\n                type: att.type,\n                filename: att.filename,\n                url: att.url,\n                size: att.size,\n                uploadedAt: att.uploadedAt,\n                base64Data: att.base64Data,\n                chatName: att.chatName,\n                storagePath: att.storagePath\n            }));\n        onAttachmentsChange(enabledAttachments);\n    }, [\n        attachmentsWithState,\n        onAttachmentsChange\n    ]);\n    // Função para fechar o modal e notificar mudanças\n    const handleClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        notifyAttachmentsChange();\n        onClose();\n    }, [\n        notifyAttachmentsChange,\n        onClose\n    ]);\n    const toggleAttachment = (attachmentId)=>{\n        setAttachmentsWithState((prev)=>prev.map((att)=>att.id === attachmentId ? {\n                    ...att,\n                    isEnabled: !att.isEnabled\n                } : att));\n    };\n    const toggleAll = (enable)=>{\n        setAttachmentsWithState((prev)=>prev.map((att)=>({\n                    ...att,\n                    isEnabled: enable\n                })));\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const formatDate = (timestamp)=>{\n        return new Date(timestamp).toLocaleString(\"pt-BR\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const handleImageClick = (url)=>{\n        setExpandedImage(url);\n    };\n    const handleDownload = async (attachment)=>{\n        try {\n            // Usar nossa API route para contornar problemas de CORS\n            const downloadUrl = \"/api/download/attachment?url=\".concat(encodeURIComponent(attachment.url), \"&filename=\").concat(encodeURIComponent(attachment.filename));\n            // Criar link para download\n            const link = document.createElement(\"a\");\n            link.href = downloadUrl;\n            link.download = attachment.filename;\n            link.target = \"_blank\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        } catch (error) {\n            console.error(\"Erro ao baixar arquivo:\", error);\n        }\n    };\n    const enabledCount = attachmentsWithState.filter((att)=>att.isEnabled).length;\n    const totalCount = attachmentsWithState.length;\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n            onClick: (e)=>e.target === e.currentTarget && handleClose(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        scale: 0.95,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    exit: {\n                        scale: 0.95,\n                        opacity: 0\n                    },\n                    className: \"bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm border border-white/20 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden mx-4 lg:mx-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: \"Gerenciar Anexos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm mt-1\",\n                                            children: \"Selecione quais anexos incluir no pr\\xf3ximo prompt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClose,\n                                    className: \"text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-cyan-400\",\n                                                        children: enabledCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/60\",\n                                                        children: [\n                                                            \"/\",\n                                                            totalCount,\n                                                            \" anexos selecionados\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            totalCount === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-amber-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Nenhum anexo encontrado neste chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleAll(true),\n                                                className: \"px-4 py-2 bg-green-600/20 hover:bg-green-600/30 text-green-400  border border-green-500/30 rounded-lg transition-colors text-sm\",\n                                                children: \"Selecionar Todos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleAll(false),\n                                                className: \"px-4 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-400  border border-red-500/30 rounded-lg transition-colors text-sm\",\n                                                children: \"Desmarcar Todos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-6\",\n                            children: totalCount === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-8 h-8 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white mb-2\",\n                                        children: \"Nenhum anexo encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60\",\n                                        children: \"Este chat ainda n\\xe3o possui anexos. Envie imagens ou PDFs para come\\xe7ar.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: attachmentsWithState.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\\n                      border rounded-xl p-4 transition-all duration-200\\n                      \".concat(attachment.isEnabled ? \"bg-blue-600/10 border-blue-500/30 shadow-lg shadow-blue-500/10\" : \"bg-gray-600/10 border-gray-500/20\", \"\\n                    \"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>toggleAttachment(attachment.id),\n                                                    className: \"\\n                          w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all\\n                          \".concat(attachment.isEnabled ? \"bg-blue-600 border-blue-500 text-white\" : \"border-gray-400 hover:border-blue-400\", \"\\n                        \"),\n                                                    children: attachment.isEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 50\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 mb-2\",\n                                                            children: [\n                                                                attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-400 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-red-400 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-white truncate\",\n                                                                    children: attachment.filename\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/60 flex-shrink-0\",\n                                                                    children: formatFileSize(attachment.size)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-white/60 mb-3\",\n                                                            children: [\n                                                                \"Enviado em \",\n                                                                formatDate(attachment.messageTimestamp)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        attachment.type === \"image\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: attachment.url,\n                                                                    alt: attachment.filename,\n                                                                    className: \"max-w-full h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                                    style: {\n                                                                        maxHeight: \"150px\"\n                                                                    },\n                                                                    onClick: ()=>handleImageClick(attachment.url)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleImageClick(attachment.url),\n                                                                    className: \"absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70 transition-all\",\n                                                                    title: \"Expandir imagem\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDownload(attachment),\n                                                                className: \"flex items-center gap-1 text-xs text-blue-400 hover:text-blue-300 transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Baixar\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, attachment.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-t border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: enabledCount > 0 ? \"\".concat(enabledCount, \" anexo\").concat(enabledCount !== 1 ? \"s\" : \"\", \" ser\\xe1\").concat(enabledCount !== 1 ? \"\\xe3o\" : \"\", \" inclu\\xeddo\").concat(enabledCount !== 1 ? \"s\" : \"\", \" no pr\\xf3ximo prompt\") : \"Nenhum anexo ser\\xe1 inclu\\xeddo no pr\\xf3ximo prompt\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleClose,\n                                        className: \"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                                        children: \"Conclu\\xeddo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this),\n                expandedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/80 backdrop-blur-sm z-60 flex items-center justify-center p-4\",\n                    onClick: ()=>setExpandedImage(null),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.img, {\n                            initial: {\n                                scale: 0.8,\n                                opacity: 0\n                            },\n                            animate: {\n                                scale: 1,\n                                opacity: 1\n                            },\n                            exit: {\n                                scale: 0.8,\n                                opacity: 0\n                            },\n                            src: expandedImage,\n                            alt: \"Imagem expandida\",\n                            className: \"max-w-full max-h-full object-contain rounded-lg\",\n                            onClick: (e)=>e.stopPropagation()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setExpandedImage(null),\n                            className: \"absolute top-4 right-4 text-white/80 hover:text-white p-2 hover:bg-white/10 rounded-lg transition-all\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Download_Eye_FileText_Image_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\AttachmentsModal.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(AttachmentsModal, \"gmAii2nPfylSsq2yVYcttqJGSXU=\");\n_c = AttachmentsModal;\nvar _c;\n$RefreshReg$(_c, \"AttachmentsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\n"));

/***/ })

});