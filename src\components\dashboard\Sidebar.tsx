'use client';

import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { collection, getDocs, orderBy, query, updateDoc, doc, deleteDoc } from 'firebase/firestore';
import { ref, deleteObject } from 'firebase/storage';
import { db, storage } from '@/lib/firebase';
import CreateChatModal from './CreateChatModal';
import CreateFolderModal from './CreateFolderModal';
import ConfirmDeleteModal from './ConfirmDeleteModal';
import PasswordProtectedModal from './PasswordProtectedModal';

interface UserData {
  username: string;
  email: string;
  balance: number;
  createdAt: string;
}

interface Chat {
  id: string;
  name: string;
  lastMessage: string;
  lastMessageTime: string;
  folder?: string;
  password?: string;
}

interface ChatFolder {
  id: string;
  name: string;
  description?: string;
  color: string;
  chats: Chat[];
  isExpanded: boolean;
}

interface SidebarProps {
  userData: UserData;
  isOpen: boolean;
  onToggle: () => void;
  onSettingsOpen: () => void;
  onChatSelect: (chatId: string | null) => void;
  currentChat: string | null;
}

const Sidebar = forwardRef<{ reloadChats: () => void }, SidebarProps>(({
  userData,
  isOpen,
  onToggle,
  onSettingsOpen,
  onChatSelect,
  currentChat
}, ref) => {
  const [folders, setFolders] = useState<ChatFolder[]>([]);
  const [unorganizedChats, setUnorganizedChats] = useState<Chat[]>([]);
  const [createChatModalOpen, setCreateChatModalOpen] = useState(false);
  const [editChatModalOpen, setEditChatModalOpen] = useState(false);
  const [editingChat, setEditingChat] = useState<Chat | null>(null);
  const [createFolderModalOpen, setCreateFolderModalOpen] = useState(false);
  const [editFolderModalOpen, setEditFolderModalOpen] = useState(false);
  const [editingFolder, setEditingFolder] = useState<{
    id: string;
    name: string;
    description?: string;
    color: string;
    expandedByDefault: boolean;
  } | null>(null);
  const [draggedChat, setDraggedChat] = useState<string | null>(null);
  const [dragOverFolder, setDragOverFolder] = useState<string | null>(null);
  const [hoveredFolder, setHoveredFolder] = useState<string | null>(null);

  // Estados para modais de confirmação
  const [deleteConfirmModal, setDeleteConfirmModal] = useState<{
    isOpen: boolean;
    type: 'chat' | 'folder';
    id: string;
    name: string;
  }>({
    isOpen: false,
    type: 'chat',
    id: '',
    name: ''
  });
  const [isDeleting, setIsDeleting] = useState(false);

  // Estados para modal de senha
  const [passwordModal, setPasswordModal] = useState<{
    isOpen: boolean;
    chatId: string;
    chatName: string;
    action: 'access' | 'edit' | 'delete';
  }>({
    isOpen: false,
    chatId: '',
    chatName: '',
    action: 'access'
  });

  const loadChats = async () => {
    try {
      // Carregar chats
      const chatsRef = collection(db, 'usuarios', userData.username, 'conversas');
      const chatsQuery = query(chatsRef, orderBy('lastUpdatedAt', 'desc'));
      const chatsSnapshot = await getDocs(chatsQuery);

      const chats: Chat[] = [];
      chatsSnapshot.forEach((doc) => {
        const data = doc.data();
        chats.push({
          id: doc.id,
          name: data.name || 'Conversa sem nome',
          lastMessage: data.ultimaMensagem || 'Nenhuma mensagem ainda',
          lastMessageTime: data.ultimaMensagemEm || data.createdAt,
          folder: data.folderId,
          password: data.password
        });
      });

      // Carregar pastas
      const foldersRef = collection(db, 'usuarios', userData.username, 'pastas');
      const foldersQuery = query(foldersRef, orderBy('createdAt', 'asc'));
      const foldersSnapshot = await getDocs(foldersQuery);

      const loadedFolders: ChatFolder[] = [];
      foldersSnapshot.forEach((doc) => {
        const data = doc.data();
        const folderChats = chats.filter(chat => chat.folder === doc.id);

        loadedFolders.push({
          id: doc.id,
          name: data.name,
          description: data.description,
          color: data.color || '#3B82F6',
          isExpanded: data.expandedByDefault !== false,
          chats: folderChats
        });
      });

      // Chats sem pasta
      const unorganized = chats.filter(chat => !chat.folder);

      setFolders(loadedFolders);
      setUnorganizedChats(unorganized);

    } catch (error) {
      console.error('Erro ao carregar chats e pastas:', error);
    }
  };

  useEffect(() => {
    if (userData.username) {
      loadChats();
    }
  }, [userData.username]);

  // Expor a função loadChats para o componente pai
  useImperativeHandle(ref, () => ({
    reloadChats: loadChats
  }));

  const handleNewChat = () => {
    setCreateChatModalOpen(true);
  };

  const handleNewFolder = () => {
    setCreateFolderModalOpen(true);
  };

  const handleFolderCreated = (folderId: string) => {
    console.log('Pasta criada:', folderId);
    loadChats(); // Recarregar para mostrar a nova pasta
  };

  const handleFolderUpdated = (folderId: string) => {
    console.log('Pasta atualizada:', folderId);
    loadChats(); // Recarregar para mostrar as alterações
    setEditFolderModalOpen(false);
    setEditingFolder(null);
  };

  const handleEditFolder = async (folderId: string) => {
    try {
      // Buscar dados da pasta no Firestore
      const folderDoc = await getDocs(query(collection(db, 'usuarios', userData.username, 'pastas')));
      const folderData = folderDoc.docs.find(doc => doc.id === folderId)?.data();

      if (folderData) {
        setEditingFolder({
          id: folderId,
          name: folderData.name,
          description: folderData.description,
          color: folderData.color || '#3B82F6',
          expandedByDefault: folderData.expandedByDefault !== false
        });
        setEditFolderModalOpen(true);
      }
    } catch (error) {
      console.error('Erro ao carregar dados da pasta:', error);
      alert('Erro ao carregar dados da pasta.');
    }
  };

  const handleDeleteFolder = (folderId: string, folderName: string) => {
    setDeleteConfirmModal({
      isOpen: true,
      type: 'folder',
      id: folderId,
      name: folderName
    });
  };

  const handleDeleteChat = (chatId: string, chatName: string) => {
    setDeleteConfirmModal({
      isOpen: true,
      type: 'chat',
      id: chatId,
      name: chatName
    });
  };

  const confirmDelete = async () => {
    setIsDeleting(true);

    try {
      if (deleteConfirmModal.type === 'folder') {
        // Deletar pasta
        await deleteDoc(doc(db, 'usuarios', userData.username, 'pastas', deleteConfirmModal.id));

        // Mover todos os chats da pasta para "sem pasta"
        const chatsInFolder = folders.find(f => f.id === deleteConfirmModal.id)?.chats || [];
        for (const chat of chatsInFolder) {
          await updateDoc(doc(db, 'usuarios', userData.username, 'conversas', chat.id), {
            folderId: null,
            updatedAt: new Date().toISOString()
          });
        }

        console.log('Pasta deletada:', deleteConfirmModal.id);
      } else {
        // Deletar chat
        await deleteDoc(doc(db, 'usuarios', userData.username, 'conversas', deleteConfirmModal.id));

        // Tentar deletar arquivo do Storage
        try {
          const storageRef = ref(storage, `usuarios/${userData.username}/conversas/${deleteConfirmModal.id}/chat.json`);
          await deleteObject(storageRef);
        } catch (storageError) {
          console.log('Arquivo no storage não encontrado:', storageError);
        }

        // Se era o chat ativo, limpar seleção
        if (currentChat === deleteConfirmModal.id) {
          onChatSelect('');
        }

        console.log('Chat deletado:', deleteConfirmModal.id);
      }

      // Recarregar dados
      loadChats();

      // Fechar modal
      setDeleteConfirmModal({
        isOpen: false,
        type: 'chat',
        id: '',
        name: ''
      });

    } catch (error) {
      console.error('Erro ao deletar:', error);
      alert('Erro ao deletar. Tente novamente.');
    } finally {
      setIsDeleting(false);
    }
  };

  // Funções de drag and drop
  const handleDragStart = (chatId: string) => {
    setDraggedChat(chatId);
  };

  const handleDragEnd = () => {
    setDraggedChat(null);
    setDragOverFolder(null);
  };

  const handleDragOver = (e: React.DragEvent, folderId: string | null) => {
    e.preventDefault();
    setDragOverFolder(folderId);
  };

  const handleDragLeave = () => {
    setDragOverFolder(null);
  };

  const handleDrop = async (e: React.DragEvent, folderId: string | null) => {
    e.preventDefault();

    if (!draggedChat) return;

    try {
      // Atualizar o chat no Firestore
      await updateDoc(doc(db, 'usuarios', userData.username, 'conversas', draggedChat), {
        folderId: folderId,
        updatedAt: new Date().toISOString()
      });

      console.log(`Chat ${draggedChat} movido para pasta ${folderId || 'sem pasta'}`);

      // Recarregar chats para refletir a mudança
      loadChats();

    } catch (error) {
      console.error('Erro ao mover chat:', error);
    } finally {
      setDraggedChat(null);
      setDragOverFolder(null);
    }
  };

  const handleChatCreated = (chatId: string) => {
    console.log('Chat criado:', chatId);
    // Recarregar a lista de chats
    loadChats();
    onChatSelect(chatId);
  };

  const handleEditChat = (chat: Chat) => {
    setEditingChat(chat);
    setEditChatModalOpen(true);
  };

  const handleChatUpdated = (chatId: string) => {
    console.log('Chat atualizado:', chatId);
    // Recarregar a lista de chats
    loadChats();
    setEditChatModalOpen(false);
    setEditingChat(null);
  };



  const handleHomeClick = () => {
    onChatSelect(null); // Limpar chat atual para ir para área inicial
  };

  const toggleFolder = (folderId: string) => {
    setFolders(prev => prev.map(folder =>
      folder.id === folderId
        ? { ...folder, isExpanded: !folder.isExpanded }
        : folder
    ));
  };

  const formatTime = (timeString: string) => {
    const date = new Date(timeString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 168) { // 7 dias
      return date.toLocaleDateString('pt-BR', {
        weekday: 'short'
      });
    } else {
      return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit'
      });
    }
  };

  const getFolderHexColor = (colorName: string): string => {
    const colorMap: { [key: string]: string } = {
      'blue': '#3B82F6',
      'green': '#10B981',
      'yellow': '#F59E0B',
      'red': '#EF4444',
      'purple': '#8B5CF6',
      'cyan': '#06B6D4',
      'lime': '#84CC16',
      'orange': '#F97316',
      'pink': '#EC4899',
      'gray': '#6B7280'
    };
    return colorMap[colorName] || colorName;
  };

  // Funções para chat protegido por senha
  const checkChatPassword = async (chatId: string, inputPassword: string): Promise<boolean> => {
    try {
      const chatDoc = await getDocs(query(collection(db, 'usuarios', userData.username, 'conversas')));
      const chatData = chatDoc.docs.find(doc => doc.id === chatId)?.data();

      if (chatData && chatData.password) {
        return chatData.password === inputPassword;
      }

      return true; // Se não tem senha, permite acesso
    } catch (error) {
      console.error('Erro ao verificar senha:', error);
      return false;
    }
  };

  const handleProtectedAction = (chatId: string, chatName: string, action: 'access' | 'edit' | 'delete') => {
    const chat = [...unorganizedChats, ...folders.flatMap(f => f.chats)].find(c => c.id === chatId);

    if (chat?.password) {
      // Chat protegido, abrir modal de senha
      setPasswordModal({
        isOpen: true,
        chatId,
        chatName,
        action
      });
    } else {
      // Chat não protegido, executar ação diretamente
      executeAction(chatId, chatName, action);
    }
  };

  const executeAction = (chatId: string, chatName: string, action: 'access' | 'edit' | 'delete') => {
    switch (action) {
      case 'access':
        onChatSelect(chatId);
        break;
      case 'edit':
        const chat = [...unorganizedChats, ...folders.flatMap(f => f.chats)].find(c => c.id === chatId);
        if (chat) {
          handleEditChat(chat);
        }
        break;
      case 'delete':
        handleDeleteChat(chatId, chatName);
        break;
    }
  };

  const handlePasswordSuccess = () => {
    executeAction(passwordModal.chatId, passwordModal.chatName, passwordModal.action);
    setPasswordModal({
      isOpen: false,
      chatId: '',
      chatName: '',
      action: 'access'
    });
  };

  const formatBalance = (balance: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(balance);
  };

  return (
    <>
      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95
        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header com perfil do usuário */}
          <div className="p-6 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-lg">
                  {userData.username.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1">
                <h3 className="text-white font-semibold text-lg">{userData.username}</h3>
                <p className="text-blue-200 text-sm">{formatBalance(userData.balance)}</p>
              </div>
              <button
                onClick={onSettingsOpen}
                className="text-blue-200 hover:text-white transition-colors p-1"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>
            </div>
          </div>

          {/* Botões de navegação */}
          <div className="p-4 space-y-2">
            <button
              onClick={handleHomeClick}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg 
                       transition-all duration-200 flex items-center space-x-3 font-medium"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              <span>Área Inicial</span>
            </button>

            <div className="flex space-x-2">
              <button
                onClick={handleNewChat}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg
                         transition-all duration-200 flex items-center justify-center space-x-2 font-medium"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                <span>Nova Conversa</span>
              </button>

              <button
                onClick={handleNewFolder}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg
                         transition-all duration-200 flex items-center justify-center font-medium"
                title="Nova Pasta"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                </svg>
              </button>
            </div>
          </div>

          {/* Seção de conversas */}
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent">
            {/* Header das conversas */}
            <div className="px-4 py-2 border-b border-white/10">
              <div className="flex items-center justify-between">
                <h4 className="text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                  <span>Conversas</span>
                </h4>
              </div>
            </div>

            {/* Lista de pastas */}
            <div className="px-2 py-2">
              {folders.map((folder) => (
                <div
                  key={folder.id}
                  className="mb-2"
                  onDragOver={(e) => handleDragOver(e, folder.id)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, folder.id)}
                >
                  {/* Cabeçalho da Pasta */}
                  <div
                    className={`group relative rounded-lg transition-all duration-300 cursor-pointer ${
                      hoveredFolder === folder.id ? 'bg-blue-800/40' : 'hover:bg-blue-800/30'
                    } ${dragOverFolder === folder.id ? 'bg-blue-500/30 ring-2 ring-blue-400/50' : ''}`}
                    onMouseEnter={() => setHoveredFolder(folder.id)}
                    onMouseLeave={() => setHoveredFolder(null)}
                    onClick={() => toggleFolder(folder.id)}
                  >
                    <div className="flex items-center justify-between p-3">
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        {/* Ícone de expansão */}
                        <div className="flex-shrink-0">
                          <svg
                            className={`w-4 h-4 text-blue-300 transition-transform duration-200 ${
                              folder.isExpanded ? 'rotate-90' : ''
                            }`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>

                        {/* Ícone da pasta */}
                        <div
                          className="w-6 h-6 rounded flex items-center justify-center flex-shrink-0"
                          style={{ backgroundColor: getFolderHexColor(folder.color) + '40' }}
                        >
                          <svg
                            className="w-4 h-4"
                            style={{ color: getFolderHexColor(folder.color) }}
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z" />
                          </svg>
                        </div>

                        {/* Nome da pasta e contador */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <h4 className="text-base font-semibold text-blue-100 truncate">
                              {folder.name}
                            </h4>
                            <span className="text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full">
                              {folder.chats.length}
                            </span>
                          </div>
                          {folder.description && (
                            <p className="text-xs text-blue-300/60 truncate mt-0.5">
                              {folder.description}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Ações da pasta (visíveis no hover) */}
                      <div className={`flex items-center space-x-1 transition-all duration-300 ${
                        hoveredFolder === folder.id ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2'
                      }`}>
                        <button
                          className="p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200"
                          title="Editar pasta"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditFolder(folder.id);
                          }}
                        >
                          <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          className="p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200"
                          title="Deletar pasta"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteFolder(folder.id, folder.name);
                          }}
                        >
                          <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Lista de conversas (quando expandida) */}
                  {folder.isExpanded && (
                    <div className="ml-6 mt-2 space-y-1">
                      {folder.chats.map((chat) => (
                        <div
                          key={chat.id}
                          draggable
                          onDragStart={() => handleDragStart(chat.id)}
                          onDragEnd={handleDragEnd}
                          className={`group relative rounded-xl transition-all duration-300 cursor-move ${
                            currentChat === chat.id
                              ? 'bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg'
                              : 'hover:bg-blue-800/30 border border-transparent'
                          } ${draggedChat === chat.id ? 'opacity-50 scale-95' : ''}`}
                        >
                          <button
                            className="w-full text-left p-3 flex items-start space-x-3"
                            onClick={() => handleProtectedAction(chat.id, chat.name, 'access')}
                          >
                            <div className={`w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative ${
                              currentChat === chat.id
                                ? 'bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30'
                                : 'bg-blue-700/50 group-hover:bg-blue-600/70'
                            }`}>
                              {/* Ícone de senha se o chat for protegido */}
                              {chat.password && (
                                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900">
                                  <svg className="w-2 h-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3}
                                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                  </svg>
                                </div>
                              )}
                              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                              </svg>
                            </div>

                            <div className="overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20">
                              <h4 className={`truncate text-sm font-semibold mb-1 ${
                                currentChat === chat.id
                                  ? 'text-white'
                                  : 'text-blue-100 group-hover:text-white'
                              }`}>
                                {chat.name}
                              </h4>
                              <p className={`truncate text-xs leading-relaxed transition-all duration-300 ${
                                currentChat === chat.id
                                  ? 'text-blue-200/80'
                                  : 'text-blue-300/70 group-hover:text-blue-200'
                              }`}>
                                {chat.lastMessage || 'Nenhuma mensagem ainda...'}
                              </p>
                              {chat.lastMessageTime && (
                                <span className={`text-xs mt-1 block ${
                                  currentChat === chat.id
                                    ? 'text-blue-300/60'
                                    : 'text-blue-400/50 group-hover:text-blue-300/70'
                                }`}>
                                  {formatTime(chat.lastMessageTime)}
                                </span>
                              )}
                            </div>
                          </button>

                          {/* Ações da conversa */}
                          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300">
                            <button
                              className="p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm"
                              title="Editar"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleProtectedAction(chat.id, chat.name, 'edit');
                              }}
                            >
                              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button
                              className="p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm"
                              title="Deletar"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleProtectedAction(chat.id, chat.name, 'delete');
                              }}
                            >
                              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Seção sem pasta - sempre visível */}
            <div
              className="px-2 py-2"
              onDragOver={(e) => handleDragOver(e, null)}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, null)}
            >
              <div className="px-3 py-2">
                <h5 className={`text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 ${
                  dragOverFolder === null && draggedChat ? 'text-blue-400' : ''
                }`}>
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                  <span>Sem Pasta {dragOverFolder === null && draggedChat ? '(Solte aqui para remover da pasta)' : ''}</span>
                </h5>
              </div>
              <div className={`space-y-1 min-h-[60px] transition-all duration-200 ${
                dragOverFolder === null && draggedChat ? 'bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50' : ''
              }`}>
                {unorganizedChats.length === 0 ? (
                  <div className="text-center py-4">
                    <div className="w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2">
                      <svg className="w-4 h-4 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                          d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    <p className="text-white/40 text-xs">Nenhuma conversa sem pasta</p>
                  </div>
                ) : (
                  unorganizedChats.map((chat) => (
                    <ChatItem
                      key={chat.id}
                      chat={chat}
                      isActive={currentChat === chat.id}
                      onClick={() => handleProtectedAction(chat.id, chat.name, 'access')}
                      onEdit={(chatId, chatName) => handleProtectedAction(chatId, chatName, 'edit')}
                      onDelete={(chatId, chatName) => handleProtectedAction(chatId, chatName, 'delete')}
                      onDragStart={handleDragStart}
                      onDragEnd={handleDragEnd}
                      isDragging={draggedChat === chat.id}
                    />
                  ))
                )}
              </div>
            </div>

            {/* Estado vazio */}
            {folders.length === 0 && unorganizedChats.length === 0 && (
              <div className="px-4 py-8 text-center">
                <div className="text-white/30 mb-2">
                  <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <p className="text-white/50 text-sm">Nenhuma conversa ainda</p>
                <p className="text-white/30 text-xs mt-1">Clique em "Nova Conversa" para começar</p>
              </div>
            )}
          </div>

          {/* Botão de fechar (mobile) */}
          <div className="lg:hidden p-4 border-t border-white/10">
            <button
              onClick={onToggle}
              className="w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg 
                       transition-all duration-200 flex items-center justify-center space-x-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span>Fechar</span>
            </button>
          </div>
        </div>
      </div>

      {/* Modal de criar conversa */}
      <CreateChatModal
        isOpen={createChatModalOpen}
        onClose={() => setCreateChatModalOpen(false)}
        username={userData.username}
        onChatCreated={handleChatCreated}
      />

      {/* Modal de editar conversa */}
      {editingChat && (
        <CreateChatModal
          isOpen={editChatModalOpen}
          onClose={() => {
            setEditChatModalOpen(false);
            setEditingChat(null);
          }}
          username={userData.username}
          onChatCreated={handleChatUpdated}
          editingChat={editingChat}
        />
      )}

      {/* Modal de criar pasta */}
      <CreateFolderModal
        isOpen={createFolderModalOpen}
        onClose={() => setCreateFolderModalOpen(false)}
        username={userData.username}
        onFolderCreated={handleFolderCreated}
      />

      {/* Modal de editar pasta */}
      <CreateFolderModal
        isOpen={editFolderModalOpen}
        onClose={() => {
          setEditFolderModalOpen(false);
          setEditingFolder(null);
        }}
        username={userData.username}
        onFolderCreated={handleFolderUpdated}
        editingFolder={editingFolder}
      />

      {/* Modal de confirmação de exclusão */}
      <ConfirmDeleteModal
        isOpen={deleteConfirmModal.isOpen}
        onClose={() => setDeleteConfirmModal({ isOpen: false, type: 'chat', id: '', name: '' })}
        onConfirm={confirmDelete}
        title={deleteConfirmModal.type === 'folder' ? 'Deletar Pasta' : 'Deletar Conversa'}
        message={
          deleteConfirmModal.type === 'folder'
            ? 'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela serão movidas para "Sem Pasta".'
            : 'Tem certeza que deseja deletar esta conversa? Todas as mensagens serão perdidas permanentemente.'
        }
        itemName={deleteConfirmModal.name}
        isLoading={isDeleting}
      />

      {/* Modal de senha para chat protegido */}
      <PasswordProtectedModal
        isOpen={passwordModal.isOpen}
        onClose={() => setPasswordModal({ isOpen: false, chatId: '', chatName: '', action: 'access' })}
        onSuccess={handlePasswordSuccess}
        chatName={passwordModal.chatName}
        onPasswordSubmit={(password) => checkChatPassword(passwordModal.chatId, password)}
      />
    </>
  );
});

Sidebar.displayName = 'Sidebar';

export default Sidebar;

// Componente para item de chat
interface ChatItemProps {
  chat: Chat;
  isActive: boolean;
  onClick: () => void;
  onEdit: (chatId: string, chatName: string) => void;
  onDelete: (chatId: string, chatName: string) => void;
  onDragStart: (chatId: string) => void;
  onDragEnd: () => void;
  isDragging: boolean;
}

function ChatItem({ chat, isActive, onClick, onEdit, onDelete, onDragStart, onDragEnd, isDragging }: ChatItemProps) {
  const formatTime = (timeString: string) => {
    const date = new Date(timeString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 dias
      return `há ${Math.floor(diffInHours / 24)} dias`;
    } else {
      return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit(chat.id, chat.name);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(chat.id, chat.name);
  };

  return (
    <div
      draggable
      onDragStart={() => onDragStart(chat.id)}
      onDragEnd={onDragEnd}
      className={`relative w-full rounded-lg transition-all duration-200 group cursor-move ${
        isActive
          ? 'bg-blue-600/50'
          : 'hover:bg-white/5'
      } ${isDragging ? 'opacity-50 scale-95' : ''}`}
    >
      <button
        onClick={onClick}
        className="w-full text-left px-3 py-3 text-white/80 hover:text-white"
      >
        <div className="flex items-start space-x-3">
          <div className={`w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative ${
            isActive
              ? 'bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30'
              : 'bg-blue-700/50 group-hover:bg-blue-600/70'
          }`}>
            {/* Ícone de senha se o chat for protegido */}
            {chat.password && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900">
                <svg className="w-2 h-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
            )}
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <h6 className="font-medium text-sm truncate">{chat.name}</h6>
              <span className="text-xs text-white/50 flex-shrink-0 ml-2 group-hover:opacity-0 transition-opacity duration-200">
                {formatTime(chat.lastMessageTime)}
              </span>
            </div>
            <p className="text-xs text-white/60 line-clamp-2 leading-relaxed">
              {chat.lastMessage}
            </p>
          </div>
        </div>
      </button>

      {/* Botões de ação - aparecem no hover */}
      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1">
        <button
          onClick={handleEdit}
          className="p-1.5 bg-blue-600/80 hover:bg-blue-600 text-white rounded-md transition-colors duration-200"
          title="Configurar chat"
        >
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                  d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>
        <button
          onClick={handleDelete}
          className="p-1.5 bg-red-600/80 hover:bg-red-600 text-white rounded-md transition-colors duration-200"
          title="Deletar chat"
        >
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </div>
  );
}


