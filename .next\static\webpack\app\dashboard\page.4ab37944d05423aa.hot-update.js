"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Upperbar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/Upperbar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Upperbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Hook para tempo de sessão\nconst useSessionTime = (chatId)=>{\n    _s();\n    const [sessionTime, setSessionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0s\");\n    const [startTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Função para formatar tempo de forma inteligente\n    const formatTime = (timeInMs)=>{\n        const totalSeconds = Math.floor(timeInMs / 1000);\n        const totalMinutes = Math.floor(totalSeconds / 60);\n        const totalHours = Math.floor(totalMinutes / 60);\n        const totalDays = Math.floor(totalHours / 24);\n        const totalMonths = Math.floor(totalDays / 30);\n        const seconds = totalSeconds % 60;\n        const minutes = totalMinutes % 60;\n        const hours = totalHours % 24;\n        const days = totalDays % 30;\n        // Menos de 1 minuto: apenas segundos\n        if (totalMinutes === 0) {\n            return \"\".concat(totalSeconds, \"s\");\n        }\n        // Menos de 1 hora: MM:SS\n        if (totalHours === 0) {\n            return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        // Menos de 1 dia: HH:MM:SS\n        if (totalDays === 0) {\n            return \"\".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        // Menos de 1 mês: Xd HH:MM\n        if (totalMonths === 0) {\n            return \"\".concat(days, \"d \").concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n        }\n        // 1 mês ou mais: Xm Xd HH:MM\n        return \"\".concat(totalMonths, \"m \").concat(days, \"d \").concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatId) {\n            intervalRef.current = setInterval(()=>{\n                const elapsed = Date.now() - startTime;\n                const formattedTime = formatTime(elapsed);\n                setSessionTime(formattedTime);\n            }, 1000);\n        }\n        return ()=>{\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n                intervalRef.current = null;\n            }\n        };\n    }, [\n        chatId,\n        startTime\n    ]);\n    return sessionTime;\n};\n_s(useSessionTime, \"D9h1eTTjB/UJUr/8kCtwXx6uawQ=\");\nfunction Upperbar(param) {\n    let { currentChat, chatName, aiModel, isFullscreen, onFullscreenToggle, onDownload, isLoading = false, attachmentsCount = 0, aiMetadata } = param;\n    _s1();\n    const sessionTime = useSessionTime(currentChat);\n    const handleAttachments = ()=>{\n        console.log(\"Anexos clicado\");\n    };\n    const handleStats = ()=>{\n        console.log(\"Estat\\xedsticas clicado\");\n    };\n    const handleDownload = ()=>{\n        if (onDownload) {\n            onDownload();\n        } else {\n            console.log(\"Download clicado\");\n        }\n    };\n    const toggleFullscreen = ()=>{\n        onFullscreenToggle();\n    };\n    const currentModel = aiModel || \"GPT-4.1 Nano\";\n    const displayChatName = chatName || (currentChat ? \"Chat \".concat(currentChat) : \"Nova Conversa\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-14 sm:h-16 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border-b border-blue-700/30 shadow-xl relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-between px-3 sm:px-4 lg:px-6 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-900/30 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm font-medium text-blue-100 truncate\",\n                                        children: currentModel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    (aiMetadata === null || aiMetadata === void 0 ? void 0 : aiMetadata.usedCoT) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-1.5 sm:px-2 py-0.5 sm:py-1 bg-cyan-600/20 text-cyan-300 text-xs rounded-full border border-cyan-500/30 hidden sm:block\",\n                                        children: \"CoT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-4 sm:h-6 bg-blue-600/30 hidden sm:block\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1.5 sm:space-x-2 bg-blue-900/20 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 hidden sm:flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 text-blue-300\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm text-blue-200 font-mono\",\n                                        children: sessionTime\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-1/2 transform -translate-x-1/2 hidden sm:block\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 sm:h-8 bg-blue-700/30 rounded-lg sm:rounded-xl w-32 sm:w-48 backdrop-blur-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 shadow-lg max-w-xs lg:max-w-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-cyan-400 rounded-full shadow-lg shadow-cyan-400/50 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-sm sm:text-lg font-semibold text-white truncate\",\n                                    children: displayChatName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleFullscreen,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 hidden sm:block\",\n                                title: \"Tela cheia\",\n                                children: isFullscreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAttachments,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 relative\",\n                                title: \"Anexos\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    attachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 bg-cyan-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium shadow-lg\",\n                                        children: attachmentsCount > 99 ? \"99+\" : attachmentsCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStats,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105\",\n                                title: \"Estat\\xedsticas\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownload,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/30 hover:scale-105 border border-blue-500/30\",\n                                title: \"Download\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s1(Upperbar, \"QJdsJtw+rWTUW8x2bLmlvIfMLsk=\", false, function() {\n    return [\n        useSessionTime\n    ];\n});\n_c = Upperbar;\nvar _c;\n$RefreshReg$(_c, \"Upperbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\n"));

/***/ })

});