'use client';

import { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { db } from '@/lib/firebase';
import Sidebar from '@/components/dashboard/Sidebar';
import ChatArea from '@/components/dashboard/ChatArea';
import SettingsModal from '@/components/dashboard/SettingsModal';

interface UserData {
  username: string;
  email: string;
  balance: number;
  createdAt: string;
}

export default function Dashboard() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [currentChat, setCurrentChat] = useState<string | null>(null);
  const sidebarRef = useRef<{ reloadChats: () => void }>(null);

  // Função para lidar com a criação de chat automático
  const handleChatCreated = (chatId: string) => {
    setCurrentChat(chatId);
    // Recarregar a sidebar para mostrar o novo chat
    sidebarRef.current?.reloadChats();
  };

  // Redirecionar se não estiver logado
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  // Buscar dados do usuário
  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) return;

      try {
        // Buscar todos os documentos na coleção usuarios para encontrar o usuário pelo email
        const { collection, query, where, getDocs } = await import('firebase/firestore');
        const usuariosRef = collection(db, 'usuarios');
        const q = query(usuariosRef, where('email', '==', user.email));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          // Usuário encontrado
          const userDoc = querySnapshot.docs[0];
          const data = userDoc.data();
          setUserData({
            username: data.username || user.email?.split('@')[0] || 'Usuário',
            email: data.email || user.email || '',
            balance: data.balance || 0,
            createdAt: data.createdAt || new Date().toISOString()
          });
        } else {
          // Se não encontrar o documento, criar dados padrão
          setUserData({
            username: user.email?.split('@')[0] || 'Usuário',
            email: user.email || '',
            balance: 0,
            createdAt: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Erro ao buscar dados do usuário:', error);
        // Dados padrão em caso de erro
        setUserData({
          username: user.email?.split('@')[0] || 'Usuário',
          email: user.email || '',
          balance: 0,
          createdAt: new Date().toISOString()
        });
      } finally {
        setLoading(false);
      }
    };

    if (user && !authLoading) {
      fetchUserData();
    }
  }, [user, authLoading]);

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-rafthor flex items-center justify-center">
        <div className="text-white text-xl">Carregando...</div>
      </div>
    );
  }

  if (!user || !userData) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-rafthor flex">
      {/* Sidebar */}
      <Sidebar
        ref={sidebarRef}
        userData={userData}
        isOpen={sidebarOpen}
        onToggle={() => setSidebarOpen(!sidebarOpen)}
        onSettingsOpen={() => setSettingsOpen(true)}
        onChatSelect={setCurrentChat}
        currentChat={currentChat}
      />

      {/* Área principal */}
      <div className="flex-1 flex flex-col lg:ml-80 h-screen overflow-hidden">
        {/* Header mobile */}
        <div className="lg:hidden bg-white/10 backdrop-blur-sm border-b border-white/20 p-4">
          <button
            onClick={() => setSidebarOpen(true)}
            className="text-white hover:text-white/80 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Área do chat */}
        <ChatArea
          currentChat={currentChat}
          onChatCreated={handleChatCreated}
        />
      </div>

      {/* Modal de configurações */}
      <SettingsModal
        isOpen={settingsOpen}
        onClose={() => setSettingsOpen(false)}
        userData={userData}
        onUserDataUpdate={setUserData}
      />

      {/* Overlay mobile */}
      {sidebarOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 z-40"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}
