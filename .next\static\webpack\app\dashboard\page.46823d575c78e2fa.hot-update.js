"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeAttachments, setActiveAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado\n    const saveLastUsedModel = async (modelId)=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            // Verificar se o documento existe\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                // Atualizar documento existente\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                });\n            } else {\n                // Criar novo documento\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                }, {\n                    merge: true\n                });\n            }\n            console.log(\"Last used model saved:\", modelId);\n        } catch (error) {\n            console.error(\"Error saving last used model:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado\n    const loadLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        saveLastUsedModel(modelId);\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments)=>{\n        console.log(\"=== DEBUG: CHATAREA HANDLE SEND MESSAGE ===\");\n        console.log(\"Mensagem:\", message);\n        console.log(\"Anexos recebidos:\", (attachments === null || attachments === void 0 ? void 0 : attachments.length) || 0);\n        console.log(\"Anexos detalhes:\", JSON.stringify(attachments, null, 2));\n        // Filtrar apenas anexos ativos\n        const activeAttachmentsToSend = getActiveAttachments(attachments);\n        console.log(\"Anexos ativos para envio:\", activeAttachmentsToSend.length);\n        console.log(\"Anexos ativos detalhes:\", JSON.stringify(activeAttachmentsToSend, null, 2));\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            var _msg_attachments;\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // Carregar o nome do chat recém-criado com um pequeno delay\n                setTimeout(()=>{\n                    if (chatIdToUse) {\n                        loadChatName(chatIdToUse);\n                    }\n                }, 100);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: attachments || []\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString()\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                setChatName(chatData.name || \"Conversa sem nome\");\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== DEBUG: MENSAGENS CONVERTIDAS ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado quando o componente montar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadLastUsedModel();\n        }\n    }, [\n        user\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n        } else if (!currentChat) {\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        const messageIndex = messages.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        const messageToRegenerate = messages[messageIndex];\n        // Remover todas as mensagens a partir da mensagem selecionada (inclusive)\n        const messagesBeforeRegeneration = messages.slice(0, messageIndex);\n        setMessages(messagesBeforeRegeneration);\n        // Preparar o conteúdo da mensagem para regenerar\n        setMessage(messageToRegenerate.content);\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        try {\n            // Deletar mensagens posteriores do Firebase Storage\n            for(let i = messageIndex; i < messages.length; i++){\n                const msgToDelete = messages[i];\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString()\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao atualizar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao atualizar mensagem:\", error);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: chatInterfaceRef.current.scrollHeight,\n            behavior: \"smooth\"\n        });\n    };\n    const handleFullscreenToggle = ()=>{\n        setIsFullscreen(!isFullscreen);\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setActiveAttachments((prev)=>{\n            if (prev.includes(attachmentId)) {\n                // Remover da lista de ativos\n                return prev.filter((id)=>id !== attachmentId);\n            } else {\n                // Adicionar à lista de ativos\n                return [\n                    ...prev,\n                    attachmentId\n                ];\n            }\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        return attachments.filter((attachment)=>activeAttachments.length === 0 || activeAttachments.includes(attachment.id));\n    };\n    // Sincronizar estado quando currentChat mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Limpar mensagens quando mudar para um chat diferente ou para área inicial\n        if (currentChat !== actualChatId) {\n            setMessages([]);\n        }\n        setActualChatId(currentChat);\n    }, [\n        currentChat\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen \".concat(isFullscreen ? \"fixed inset-0 z-50 bg-gradient-rafthor\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                isFullscreen: isFullscreen,\n                onFullscreenToggle: handleFullscreenToggle,\n                onDownload: handleDownloadModal,\n                isLoading: isLoading,\n                attachmentsCount: 0,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0 overflow-hidden\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 734,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 733,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 746,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 763,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 771,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 716,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"749WcwwgOpJgoAig8fKGk7uj6is=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});