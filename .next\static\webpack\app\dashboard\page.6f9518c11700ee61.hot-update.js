"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/attachmentService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/attachmentService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachmentService: function() { return /* binding */ attachmentService; }\n/* harmony export */ });\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass AttachmentService {\n    /**\n   * Valida se o arquivo é suportado\n   */ validateFile(file) {\n        // Verificar tamanho\n        if (file.size > this.MAX_FILE_SIZE) {\n            return {\n                isValid: false,\n                error: \"Arquivo muito grande. M\\xe1ximo permitido: 10MB\"\n            };\n        }\n        // Verificar tipo\n        const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n        const isPdf = file.type === this.SUPPORTED_PDF_TYPE;\n        if (!isImage && !isPdf) {\n            return {\n                isValid: false,\n                error: \"Tipo de arquivo n\\xe3o suportado. Use PNG, JPEG, WebP ou PDF\"\n            };\n        }\n        return {\n            isValid: true\n        };\n    }\n    /**\n   * Converte arquivo para base64\n   */ async fileToBase64(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                // Remover o prefixo data:type;base64,\n                const base64 = result.split(\",\")[1];\n                resolve(base64);\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(file);\n        });\n    }\n    /**\n   * Gera ID único para anexo\n   */ generateAttachmentId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Faz upload do arquivo para Firebase Storage\n   */ async uploadAttachment(params) {\n        const { file, username, chatId } = params;\n        // Validar arquivo\n        const validation = this.validateFile(file);\n        if (!validation.isValid) {\n            throw new Error(validation.error);\n        }\n        try {\n            // Gerar ID único para o anexo\n            const attachmentId = this.generateAttachmentId();\n            // Determinar tipo\n            const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n            const type = isImage ? \"image\" : \"pdf\";\n            // Criar caminho no Storage\n            const storagePath = \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/anexos/\").concat(attachmentId, \"_\").concat(file.name);\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, storagePath);\n            // Upload do arquivo\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.uploadBytes)(storageRef, file);\n            // Obter URL de download\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(storageRef);\n            // Preparar metadados\n            const metadata = {\n                id: attachmentId,\n                type,\n                filename: file.name,\n                url: downloadURL,\n                size: file.size,\n                uploadedAt: Date.now(),\n                storagePath,\n                isActive: true // Por padrão, anexos são ativos\n            };\n            // Para PDFs, também converter para base64 para envio ao OpenRouter\n            let base64Data;\n            if (type === \"pdf\") {\n                base64Data = await this.fileToBase64(file);\n                metadata.base64Data = base64Data;\n            }\n            return {\n                metadata,\n                base64Data\n            };\n        } catch (error) {\n            console.error(\"Erro ao fazer upload do anexo:\", error);\n            throw new Error(\"Falha no upload do arquivo. Tente novamente.\");\n        }\n    }\n    /**\n   * Processa múltiplos arquivos\n   */ async uploadMultipleAttachments(files, username, chatId) {\n        const results = [];\n        for (const file of files){\n            try {\n                const result = await this.uploadAttachment({\n                    file,\n                    username,\n                    chatId\n                });\n                results.push(result);\n            } catch (error) {\n                console.error(\"Erro ao processar arquivo \".concat(file.name, \":\"), error);\n            // Continuar com os outros arquivos\n            }\n        }\n        return results;\n    }\n    /**\n   * Prepara anexos para envio ao OpenRouter\n   */ prepareAttachmentsForOpenRouter(attachments) {\n        const openRouterContent = [];\n        for (const attachment of attachments){\n            if (attachment.type === \"image\") {\n                // Para imagens, usar URL\n                openRouterContent.push({\n                    type: \"image_url\",\n                    image_url: {\n                        url: attachment.url\n                    }\n                });\n            } else if (attachment.type === \"pdf\" && attachment.base64Data) {\n                // Para PDFs, usar base64 com formato file\n                const dataUrl = \"data:application/pdf;base64,\".concat(attachment.base64Data);\n                openRouterContent.push({\n                    type: \"file\",\n                    file: {\n                        filename: attachment.filename,\n                        file_data: dataUrl\n                    }\n                });\n            }\n        }\n        return openRouterContent;\n    }\n    /**\n   * Prepara plugins para PDFs (engine mistral-ocr)\n   */ preparePDFPlugins(attachments) {\n        // Filtrar apenas anexos ativos\n        const activeAttachments = attachments.filter((att)=>att.isActive !== false);\n        const hasPDF = activeAttachments.some((att)=>att.type === \"pdf\");\n        if (!hasPDF) {\n            return [];\n        }\n        return [\n            {\n                id: \"file-parser\",\n                pdf: {\n                    engine: \"mistral-ocr\"\n                }\n            }\n        ];\n    }\n    /**\n   * Limpa anexos temporários (se necessário)\n   */ async cleanupTemporaryAttachments(attachments) {\n        // Implementar limpeza se necessário\n        // Por enquanto, mantemos os arquivos no Storage\n        console.log(\"Cleanup de anexos tempor\\xe1rios:\", attachments.length);\n    }\n    constructor(){\n        this.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n        ;\n        this.SUPPORTED_IMAGE_TYPES = [\n            \"image/png\",\n            \"image/jpeg\",\n            \"image/webp\"\n        ];\n        this.SUPPORTED_PDF_TYPE = \"application/pdf\";\n    }\n}\n// Exportar instância singleton\nconst attachmentService = new AttachmentService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (attachmentService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/attachmentService.ts\n"));

/***/ })

});