"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/InputBar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/InputBar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InputBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AI_MODELS = [\n    {\n        id: \"gpt-4.1-nano\",\n        name: \"GPT-4.1 Nano\",\n        description: \"R\\xe1pido e eficiente\"\n    },\n    {\n        id: \"gpt-4-turbo\",\n        name: \"GPT-4 Turbo\",\n        description: \"Mais poderoso\"\n    },\n    {\n        id: \"claude-3-sonnet\",\n        name: \"Claude 3 Sonnet\",\n        description: \"Criativo e preciso\"\n    },\n    {\n        id: \"gemini-pro\",\n        name: \"Gemini Pro\",\n        description: \"Multimodal\"\n    }\n];\nfunction InputBar(param) {\n    let { message, setMessage, onSendMessage, isLoading, selectedModel, onModelChange, onScrollToTop, onScrollToBottom, isStreaming = false, onCancelStreaming, onOpenModelModal, username, chatId, totalAttachmentsCount = 0 } = param;\n    _s();\n    const [webSearchEnabled, setWebSearchEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedAttachments, setProcessedAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const adjustHeightTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (adjustHeightTimeoutRef.current) {\n                clearTimeout(adjustHeightTimeoutRef.current);\n            }\n        };\n    }, []);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        setMessage(e.target.value);\n        // Debounce o ajuste de altura para melhor performance\n        if (adjustHeightTimeoutRef.current) {\n            clearTimeout(adjustHeightTimeoutRef.current);\n        }\n        adjustHeightTimeoutRef.current = setTimeout(()=>{\n            adjustTextareaHeight();\n        }, 16); // ~60fps\n    };\n    const handleSend = ()=>{\n        if (((message === null || message === void 0 ? void 0 : message.trim()) || attachments.length > 0) && !isLoading && !isUploading) {\n            console.log(\"=== DEBUG: ENVIANDO MENSAGEM ===\");\n            console.log(\"Mensagem:\", message);\n            console.log(\"Anexos locais:\", attachments.length);\n            console.log(\"Anexos processados:\", processedAttachments.length);\n            console.log(\"Anexos processados detalhes:\", processedAttachments);\n            onSendMessage(processedAttachments);\n            // Limpar anexos após envio\n            setAttachments([]);\n            setProcessedAttachments([]);\n        }\n    };\n    const handleAttachment = ()=>{\n        if (fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || !username || !chatId) return;\n        setIsUploading(true);\n        try {\n            // Primeiro, adicionar arquivos localmente para preview\n            const localAttachments = [];\n            for (const file of Array.from(files)){\n                const attachment = {\n                    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),\n                    filename: file.name,\n                    type: file.type.startsWith(\"image/\") ? \"image\" : \"document\",\n                    file\n                };\n                localAttachments.push(attachment);\n            }\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...localAttachments\n                ]);\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadMultipleAttachments(Array.from(files), username, chatId);\n            // Atualizar com metadados dos arquivos processados\n            setProcessedAttachments((prev)=>[\n                    ...prev,\n                    ...uploadedAttachments.map((att)=>att.metadata)\n                ]);\n        } catch (error) {\n            console.error(\"Erro ao processar arquivos:\", error);\n            // Remover anexos que falharam\n            setAttachments((prev)=>prev.filter((att)=>!Array.from(files).some((file)=>file.name === att.filename)));\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const removeAttachment = (id)=>{\n        // Encontrar o anexo para obter o filename\n        const attachment = attachments.find((att)=>att.id === id);\n        if (attachment) {\n            // Remover do estado local\n            setAttachments((prev)=>prev.filter((att)=>att.id !== id));\n            // Remover dos anexos processados também\n            setProcessedAttachments((prev)=>prev.filter((att)=>att.filename !== attachment.filename));\n        }\n    };\n    const handleWebSearch = ()=>{\n        setWebSearchEnabled(!webSearchEnabled);\n    };\n    const adjustTextareaHeight = ()=>{\n        const textarea = textareaRef.current;\n        if (textarea) {\n            // Otimizar para evitar múltiplos reflows\n            const currentHeight = textarea.style.height;\n            textarea.style.height = \"auto\";\n            const newHeight = Math.min(textarea.scrollHeight, 120) + \"px\";\n            // Só atualizar se a altura realmente mudou\n            if (currentHeight !== newHeight) {\n                textarea.style.height = newHeight;\n            }\n        }\n    };\n    const isWebSearchEnabled = ()=>{\n        return selectedModel.includes(\"openrouter\") || selectedModel.includes(\"gpt-4\");\n    };\n    const currentModel = AI_MODELS.find((model)=>model.id === selectedModel);\n    const modelName = currentModel ? currentModel.name : selectedModel;\n    const selectedAttachmentsCount = attachments.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-3 sm:p-4 lg:p-6 border-t border-blue-700/30 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto relative z-10\",\n                children: [\n                    attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 sm:gap-3\",\n                            children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20\",\n                                    children: [\n                                        attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium\",\n                                            children: attachment.filename\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>removeAttachment(attachment.id),\n                                            className: \"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3.5 h-3.5 sm:w-4 sm:h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, attachment.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-2 sm:space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 sm:space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onOpenModelModal,\n                                                        className: \"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105\",\n                                                        title: \"Selecionar modelo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAttachment,\n                                                        disabled: isUploading,\n                                                        className: \"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105\",\n                                                        title: \"Anexar arquivo\",\n                                                        children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isWebSearchEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleWebSearch,\n                                                        className: \"p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm \".concat(webSearchEnabled ? \"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20\" : \"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20\"),\n                                                        title: \"Busca na web (OpenRouter) - \".concat(webSearchEnabled ? \"Ativada\" : \"Desativada\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    ref: textareaRef,\n                                                    value: message,\n                                                    onChange: handleInputChange,\n                                                    onKeyDown: handleKeyPress,\n                                                    className: \"w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm leading-relaxed min-h-[44px] max-h-[120px] selection:bg-blue-500/30\",\n                                                    rows: 1,\n                                                    placeholder: \"Digite sua mensagem aqui... ✨\",\n                                                    disabled: isLoading || isStreaming\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this),\n                                            isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onCancelStreaming,\n                                                className: \"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30\",\n                                                title: \"Parar gera\\xe7\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSend,\n                                                disabled: !(message === null || message === void 0 ? void 0 : message.trim()) && attachments.length === 0 || isLoading || isUploading || isStreaming,\n                                                className: \"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30\",\n                                                title: \"Enviar mensagem\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-8 left-4 flex items-center space-x-2\",\n                                        children: [\n                                            selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-300\",\n                                                        children: \"Modelo: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-cyan-300 font-medium\",\n                                                        children: modelName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedAttachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-300\",\n                                                        children: \"Enviando: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-200 font-medium\",\n                                                        children: selectedAttachmentsCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this),\n                                            totalAttachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-300\",\n                                                        children: \"Anexos: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-200 font-medium\",\n                                                        children: totalAttachmentsCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-2\",\n                                children: [\n                                    onScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onScrollToTop,\n                                        className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                        title: \"Ir para o topo\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2.5,\n                                                    d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this),\n                                    onScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onScrollToBottom,\n                                        className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                        title: \"Ir para o final\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2.5,\n                                                    d: \"M19 14l-7 7m0 0l-7-7m7 7V4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        accept: \"image/png,image/jpeg,image/webp,application/pdf\",\n                        onChange: handleFileSelect,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n_s(InputBar, \"NATbgEVliPa57PsWdc1QpV4trNc=\");\n_c = InputBar;\nvar _c;\n$RefreshReg$(_c, \"InputBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/InputBar.tsx\n"));

/***/ })

});