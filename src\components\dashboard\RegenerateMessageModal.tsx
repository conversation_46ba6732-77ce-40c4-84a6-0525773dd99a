'use client';

import { useState } from 'react';

interface RegenerateMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  messagePreview: string;
  messagesAffectedCount: number;
  isRegenerating?: boolean;
}

export default function RegenerateMessageModal({
  isOpen,
  onClose,
  onConfirm,
  messagePreview,
  messagesAffectedCount,
  isRegenerating = false
}: RegenerateMessageModalProps) {
  if (!isOpen) return null;

  // Truncar preview da mensagem se for muito longa
  const truncatedPreview = messagePreview.length > 100 
    ? messagePreview.substring(0, 100) + '...' 
    : messagePreview;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-gradient-to-br from-gray-900 via-blue-900/20 to-gray-900 border border-blue-500/30 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
        {/* Header com gradiente de regeneração */}
        <div className="bg-gradient-to-r from-blue-600/20 to-cyan-500/20 border-b border-blue-500/30 p-6">
          <div className="flex items-center space-x-3">
            {/* Ícone de regenerar */}
            <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30">
              <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-white">Regenerar Mensagem</h3>
              <p className="text-blue-300/70 text-sm">Confirme para continuar</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Mensagem de confirmação */}
          <p className="text-gray-300 text-sm leading-relaxed">
            Deseja regenerar esta mensagem? 
            {messagesAffectedCount > 0 && (
              <span className="text-orange-300">
                {' '}Isso também removerá {messagesAffectedCount} mensagem{messagesAffectedCount > 1 ? 's' : ''} posterior{messagesAffectedCount > 1 ? 'es' : ''}.
              </span>
            )}
          </p>

          {/* Preview da mensagem */}
          <div className="bg-gray-800/50 border border-gray-600/30 rounded-lg p-3">
            <p className="text-xs text-gray-400 mb-1">Mensagem a ser regenerada:</p>
            <p className="text-gray-200 text-sm leading-relaxed break-words">
              "{truncatedPreview}"
            </p>
          </div>

          {/* Aviso sobre mensagens posteriores */}
          {messagesAffectedCount > 0 && (
            <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <svg className="w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <p className="text-orange-300/80 text-xs font-medium mb-1">
                    Atenção: Mensagens posteriores serão removidas
                  </p>
                  <p className="text-orange-300/70 text-xs">
                    {messagesAffectedCount} mensagem{messagesAffectedCount > 1 ? 's' : ''} que {messagesAffectedCount > 1 ? 'vieram' : 'veio'} depois desta será{messagesAffectedCount > 1 ? 'm' : ''} permanentemente removida{messagesAffectedCount > 1 ? 's' : ''} para manter a consistência da conversa.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Informação sobre regeneração */}
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <svg className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-blue-300/80 text-xs">
                A IA gerará uma nova resposta para esta mensagem usando o mesmo contexto da conversa até este ponto.
              </p>
            </div>
          </div>
        </div>

        {/* Footer com botões */}
        <div className="bg-gray-800/30 border-t border-gray-600/30 p-6">
          <div className="flex items-center justify-end space-x-3">
            {/* Botão Cancelar */}
            <button
              onClick={onClose}
              disabled={isRegenerating}
              className="px-4 py-2 text-sm font-medium text-gray-300 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/30 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancelar
            </button>

            {/* Botão Regenerar */}
            <button
              onClick={onConfirm}
              disabled={isRegenerating}
              className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 border border-blue-500/30 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isRegenerating ? (
                <>
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Regenerando...</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span>Regenerar</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
