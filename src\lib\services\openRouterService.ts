import { AIModel, ModelCategory, ModelSortBy } from '@/lib/types/chat';

interface OpenRouterModel {
  id: string;
  name: string;
  description?: string;
  context_length: number;
  pricing: {
    prompt: string;
    completion: string;
    image?: string;
  };
  architecture?: {
    input_modalities: string[];
    output_modalities: string[];
    tokenizer: string;
  };
  created?: number;
}

class OpenRouterService {
  private cache: {
    models: AIModel[];
    timestamp: number;
  } | null = null;
  
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

  async fetchModels(): Promise<AIModel[]> {
    // Verificar cache
    if (this.cache && Date.now() - this.cache.timestamp < this.CACHE_DURATION) {
      return this.cache.models;
    }

    try {
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_OPENROUTER_API_KEY || ''}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const models: AIModel[] = data.data.map((model: OpenRouterModel) => ({
        id: model.id,
        name: model.name,
        description: model.description || '',
        context_length: model.context_length,
        pricing: {
          prompt: model.pricing.prompt,
          completion: model.pricing.completion,
          image: model.pricing.image
        },
        architecture: model.architecture,
        created: model.created,
        isFavorite: false
      }));

      // Atualizar cache
      this.cache = {
        models,
        timestamp: Date.now()
      };

      return models;
    } catch (error) {
      console.error('Error fetching OpenRouter models:', error);
      
      // Retornar cache se disponível, mesmo que expirado
      if (this.cache) {
        return this.cache.models;
      }
      
      throw error;
    }
  }

  filterByCategory(models: AIModel[], category: ModelCategory): AIModel[] {
    switch (category) {
      case 'free':
        return models.filter(model => this.isFreeModel(model));
      case 'paid':
        return models.filter(model => !this.isFreeModel(model));
      case 'favorites':
        return models.filter(model => model.isFavorite);
      default:
        return models;
    }
  }

  sortModels(models: AIModel[], sortBy: ModelSortBy): AIModel[] {
    const sortedModels = [...models];
    
    switch (sortBy) {
      case 'newest':
        return sortedModels.sort((a, b) => (b.created || 0) - (a.created || 0));
      case 'price_low':
        return sortedModels.sort((a, b) => this.getTotalPrice(a) - this.getTotalPrice(b));
      case 'price_high':
        return sortedModels.sort((a, b) => this.getTotalPrice(b) - this.getTotalPrice(a));
      case 'context_high':
        return sortedModels.sort((a, b) => b.context_length - a.context_length);
      default:
        return sortedModels;
    }
  }

  isFreeModel(model: AIModel): boolean {
    const promptPrice = parseFloat(model.pricing.prompt);
    const completionPrice = parseFloat(model.pricing.completion);
    return promptPrice === 0 && completionPrice === 0;
  }

  getTotalPrice(model: AIModel): number {
    const promptPrice = parseFloat(model.pricing.prompt);
    const completionPrice = parseFloat(model.pricing.completion);
    return promptPrice + completionPrice;
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price) * 1000000; // Multiplicar por 1M para mostrar preço por 1M tokens
    if (numPrice === 0) return 'Grátis';
    if (numPrice < 0.001) return '< $0.001';
    return `$${numPrice.toFixed(3)}`;
  }

  formatContextLength(length: number): string {
    return length.toLocaleString(); // Mostra o número completo com separadores de milhares
  }

  // Busca avançada com fuzzy matching
  searchModels(models: AIModel[], searchTerm: string, options: {
    fuzzyThreshold?: number;
    maxResults?: number;
    boostFavorites?: boolean;
  } = {}): Array<{
    model: AIModel;
    score: number;
    matchedFields: string[];
    highlightedName?: string;
    highlightedDescription?: string;
  }> {
    const {
      fuzzyThreshold = 0.6,
      maxResults = 50,
      boostFavorites = false
    } = options;

    if (!searchTerm.trim()) {
      return [];
    }

    const term = searchTerm.toLowerCase();
    const results: Array<{
      model: AIModel;
      score: number;
      matchedFields: string[];
      highlightedName?: string;
      highlightedDescription?: string;
    }> = [];

    for (const model of models) {
      let score = 0;
      const matchedFields: string[] = [];
      let highlightedName = model.name;
      let highlightedDescription = model.description || '';

      // Busca no nome (peso maior)
      if (model.name.toLowerCase().includes(term)) {
        score += 10;
        matchedFields.push('name');
        highlightedName = this.highlightText(model.name, term);
      }

      // Busca no ID (peso médio)
      if (model.id.toLowerCase().includes(term)) {
        score += 7;
        matchedFields.push('id');
      }

      // Busca na descrição (peso menor)
      if (model.description && model.description.toLowerCase().includes(term)) {
        score += 3;
        matchedFields.push('description');
        highlightedDescription = this.highlightText(model.description, term);
      }

      // Boost para favoritos
      if (boostFavorites && model.isFavorite) {
        score *= 1.5;
      }

      // Boost para modelos gratuitos se buscar por "free" ou "grátis"
      if ((term.includes('free') || term.includes('grátis')) && this.isFreeModel(model)) {
        score += 5;
      }

      // Boost para modelos caros se buscar por "expensive" ou "caro"
      if ((term.includes('expensive') || term.includes('caro')) && this.getTotalPrice(model) > 0.00002) {
        score += 5;
      }

      if (score > 0) {
        results.push({
          model,
          score,
          matchedFields,
          highlightedName,
          highlightedDescription
        });
      }
    }

    // Ordenar por score e limitar resultados
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, maxResults);
  }

  private highlightText(text: string, term: string): string {
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-300 text-black px-1 rounded">$1</mark>');
  }

  // Limpar cache
  clearCache(): void {
    this.cache = null;
  }
}

export const openRouterService = new OpenRouterService();
