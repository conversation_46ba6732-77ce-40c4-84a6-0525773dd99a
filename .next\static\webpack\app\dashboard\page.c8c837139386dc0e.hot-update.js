"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeAttachments, setActiveAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado\n    const saveLastUsedModel = async (modelId)=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            // Verificar se o documento existe\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                // Atualizar documento existente\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                });\n            } else {\n                // Criar novo documento\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                }, {\n                    merge: true\n                });\n            }\n            console.log(\"Last used model saved:\", modelId);\n        } catch (error) {\n            console.error(\"Error saving last used model:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado\n    const loadLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        saveLastUsedModel(modelId);\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments)=>{\n        console.log(\"=== DEBUG: CHATAREA HANDLE SEND MESSAGE ===\");\n        console.log(\"Mensagem:\", message);\n        console.log(\"Anexos recebidos:\", (attachments === null || attachments === void 0 ? void 0 : attachments.length) || 0);\n        console.log(\"Anexos detalhes:\", JSON.stringify(attachments, null, 2));\n        // Filtrar apenas anexos ativos\n        const activeAttachmentsToSend = getActiveAttachments(attachments);\n        console.log(\"Anexos ativos para envio:\", activeAttachmentsToSend.length);\n        console.log(\"Anexos ativos detalhes:\", JSON.stringify(activeAttachmentsToSend, null, 2));\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            var _msg_attachments;\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // Carregar o nome do chat recém-criado com um pequeno delay\n                setTimeout(()=>{\n                    if (chatIdToUse) {\n                        loadChatName(chatIdToUse);\n                    }\n                }, 100);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: activeAttachmentsToSend\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString()\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                setChatName(chatData.name || \"Conversa sem nome\");\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== DEBUG: MENSAGENS CONVERTIDAS ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado quando o componente montar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadLastUsedModel();\n        }\n    }, [\n        user\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n        } else if (!currentChat) {\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        const messageIndex = messages.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        const messageToRegenerate = messages[messageIndex];\n        // Remover todas as mensagens a partir da mensagem selecionada (inclusive)\n        const messagesBeforeRegeneration = messages.slice(0, messageIndex);\n        setMessages(messagesBeforeRegeneration);\n        // Preparar o conteúdo da mensagem para regenerar\n        setMessage(messageToRegenerate.content);\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        try {\n            // Deletar mensagens posteriores do Firebase Storage\n            for(let i = messageIndex; i < messages.length; i++){\n                const msgToDelete = messages[i];\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString()\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao atualizar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao atualizar mensagem:\", error);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: chatInterfaceRef.current.scrollHeight,\n            behavior: \"smooth\"\n        });\n    };\n    const handleFullscreenToggle = ()=>{\n        setIsFullscreen(!isFullscreen);\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setActiveAttachments((prev)=>{\n            if (prev.includes(attachmentId)) {\n                // Remover da lista de ativos\n                return prev.filter((id)=>id !== attachmentId);\n            } else {\n                // Adicionar à lista de ativos\n                return [\n                    ...prev,\n                    attachmentId\n                ];\n            }\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        return attachments.filter((attachment)=>activeAttachments.length === 0 || activeAttachments.includes(attachment.id));\n    };\n    // Sincronizar estado quando currentChat mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Limpar mensagens quando mudar para um chat diferente ou para área inicial\n        if (currentChat !== actualChatId) {\n            setMessages([]);\n            setActiveAttachments([]); // Limpar anexos ativos também\n        }\n        setActualChatId(currentChat);\n    }, [\n        currentChat\n    ]);\n    // Inicializar anexos ativos quando as mensagens são carregadas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const allAttachments = getAllChatAttachments();\n        // Por padrão, todos os anexos são ativos\n        const allAttachmentIds = allAttachments.map((att)=>att.id);\n        setActiveAttachments(allAttachmentIds);\n    }, [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen \".concat(isFullscreen ? \"fixed inset-0 z-50 bg-gradient-rafthor\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                isFullscreen: isFullscreen,\n                onFullscreenToggle: handleFullscreenToggle,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 727,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0 overflow-hidden\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 744,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 743,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 756,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 773,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 781,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: activeAttachments,\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 789,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 725,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"N9fjK3GeTm9+Z1CIquBlQyPdIMU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});